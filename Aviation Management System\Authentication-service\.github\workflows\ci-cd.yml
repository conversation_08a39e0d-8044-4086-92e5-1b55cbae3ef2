name: Authentication Service CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'Authentication-service/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'Authentication-service/**'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: aviation/authentication-service

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      sqlserver:
        image: mcr.microsoft.com/mssql/server:2022-latest
        env:
          ACCEPT_EULA: Y
          SA_PASSWORD: YourStrong!Passw0rd
        ports:
          - 1433:1433
        options: >-
          --health-cmd="/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong!Passw0rd -Q 'SELECT 1'"
          --health-interval=10s
          --health-timeout=3s
          --health-retries=10
          --health-start-period=10s
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore Authentication-service/Aviation.Authentication.Api/Aviation.Authentication.Api.csproj
    
    - name: Build
      run: dotnet build Authentication-service/Aviation.Authentication.Api/Aviation.Authentication.Api.csproj --no-restore
    
    - name: Test
      run: dotnet test Authentication-service/Aviation.Authentication.Api.Tests/Aviation.Authentication.Api.Tests.csproj --no-build --verbosity normal --collect:"XPlat Code Coverage"
      env:
        ConnectionStrings__DefaultConnection: "Server=localhost;Database=AviationAuthentication_Test;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;"
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: authentication-service

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: './Authentication-service'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  build-and-push:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./Authentication-service
        file: ./Authentication-service/Aviation.Authentication.Api/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}
    
    - name: Deploy to staging
      run: |
        kubectl apply -f Authentication-service/k8s/configmap.yaml
        kubectl apply -f Authentication-service/k8s/deployment.yaml
        kubectl rollout status deployment/authentication-service -n aviation-system --timeout=300s

  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}
    
    - name: Deploy to production
      run: |
        kubectl apply -f Authentication-service/k8s/configmap.yaml
        kubectl apply -f Authentication-service/k8s/deployment.yaml
        kubectl rollout status deployment/authentication-service -n aviation-system --timeout=300s
    
    - name: Run smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app=authentication-service -n aviation-system --timeout=300s
        # Test OAuth discovery endpoint
        AUTH_URL=$(kubectl get ingress authentication-ingress -n aviation-system -o jsonpath='{.spec.rules[0].host}')
        curl -f "https://$AUTH_URL/oauth/.well-known/oauth-authorization-server" || exit 1
        echo "Smoke tests passed"
