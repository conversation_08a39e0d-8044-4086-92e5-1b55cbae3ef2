using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Aviation.Authentication.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly IUserTokenService _userTokenService;
    private readonly IAzureAdService _azureAdService;
    private readonly IAuditService _auditService;
    private readonly ICryptoService _cryptoService;
    private readonly ILogger<AuthController> _logger;
    private readonly IConfiguration _configuration;

    public AuthController(
        IUserService userService,
        IUserTokenService userTokenService,
        IAzureAdService azureAdService,
        IAuditService auditService,
        ICryptoService cryptoService,
        ILogger<AuthController> logger,
        IConfiguration configuration)
    {
        _userService = userService;
        _userTokenService = userTokenService;
        _azureAdService = azureAdService;
        _auditService = auditService;
        _cryptoService = cryptoService;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// User login with Azure AD token or credentials
    /// </summary>
    [HttpPost("login")]
    [ProducesResponseType(typeof(UserLoginResponse), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<UserLoginResponse>> Login([FromBody] UserLoginRequest request)
    {
        var ipAddress = GetClientIpAddress();
        var userAgent = GetUserAgent();

        try
        {
            // STEP 0: Validate input
            if (string.IsNullOrWhiteSpace(request.EmailOrEmployeeId))
            {
                return BadRequest(new {
                    error = "Email or Employee ID is required",
                    errorCode = "MISSING_IDENTIFIER",
                    field = "emailOrEmployeeId",
                    message = "Please provide either an email address or employee ID."
                });
            }

            if (string.IsNullOrWhiteSpace(request.Password))
            {
                return BadRequest(new {
                    error = "Password is required",
                    errorCode = "MISSING_PASSWORD",
                    field = "password",
                    message = "Please provide a password for authentication."
                });
            }

            User? user = null;
            var isEmail = request.EmailOrEmployeeId.Contains("@");

            // STEP 1: Find user in database
            if (isEmail)
            {
                user = await _userService.GetUserByEmailAsync(request.EmailOrEmployeeId);
            }
            else
            {
                user = await _userService.GetUserByEmployeeIdAsync(request.EmailOrEmployeeId);
            }

            // STEP 2: Validate user exists in database
            if (user == null)
            {
                string? attemptedEmail = isEmail ? request.EmailOrEmployeeId : null;
                string? attemptedEmployeeId = !isEmail ? request.EmailOrEmployeeId : null;

                await _userService.LogLoginAttemptAsync(null, attemptedEmail, attemptedEmployeeId, ipAddress, false, "User not found");

                return Unauthorized(new {
                    error = "User not found",
                    errorCode = "USER_NOT_FOUND",
                    field = isEmail ? "email" : "employeeId",
                    message = $"No user found with {(isEmail ? "email" : "employee ID")} '{request.EmailOrEmployeeId}'. Please check your credentials."
                });
            }

            // STEP 3: Validate user status
            if (!user.IsActive)
            {
                await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, "Account inactive");
                return Unauthorized(new {
                    error = "Account is inactive",
                    errorCode = "ACCOUNT_INACTIVE",
                    field = "account",
                    message = $"Account for '{request.EmailOrEmployeeId}' is inactive. Please contact your administrator."
                });
            }

            // Check lockout status with detailed logging
            _logger.LogInformation("Checking lockout for user {EmployeeId}. LockoutEnd: {LockoutEnd}, CurrentTime: {CurrentTime}, IsLockedOut: {IsLockedOut}",
                user.EmployeeId, user.LockoutEnd, DateTime.UtcNow, user.IsLockedOut);

            if (user.IsLockedOut)
            {
                _logger.LogWarning("User {EmployeeId} is locked out until {LockoutEnd}. Current time: {CurrentTime}",
                    user.EmployeeId, user.LockoutEnd, DateTime.UtcNow);
                await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, "Account locked");

                var lockoutMessage = user.LockoutEnd.HasValue
                    ? $"Account is locked until {user.LockoutEnd.Value:yyyy-MM-dd HH:mm:ss} UTC due to multiple failed login attempts."
                    : "Account is locked due to multiple failed login attempts.";

                return Unauthorized(new {
                    error = "Account is locked",
                    errorCode = "ACCOUNT_LOCKED",
                    field = "account",
                    message = lockoutMessage,
                    lockoutEnd = user.LockoutEnd
                });
            }

            // Use the plain text password directly for Azure AD authentication
            _logger.LogInformation("Authenticating user {EmployeeId} with Azure AD using plain text password", user.EmployeeId);

            // Authenticate with Azure AD using the plain text password
            var azureAuthResult = await _azureAdService.AuthenticateUserAsync(user.Email, request.Password);
            if (!azureAuthResult.IsSuccess)
            {
                // Create a short failure reason for database logging (max 200 chars)
                var shortFailureReason = CreateShortFailureReason(azureAuthResult);

                // Log failed attempt (this automatically increments failed attempts and handles lockout)
                await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, shortFailureReason);

                // Return specific error message based on Azure AD error code
                var errorResponse = CreateDetailedErrorResponse(azureAuthResult, request.EmailOrEmployeeId);

                return Unauthorized(errorResponse);
            }

            // Verify the Azure AD user matches our database user
            if (azureAuthResult.User?.Email?.ToLower() != user.Email.ToLower())
            {
                await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, "Email mismatch");
                return Unauthorized(new {
                    error = "User validation failed",
                    errorCode = "EMAIL_MISMATCH",
                    field = "email",
                    message = "The authenticated user's email does not match the local database record. Please contact your administrator."
                });
            }

            _logger.LogInformation("User {EmployeeId} authenticated successfully with Azure AD", user.EmployeeId);

            // Get user permissions
            var permissions = await _userService.GetUserPermissionsAsync(user.UserId);

            // Generate JWT token for user
            var accessToken = await _userTokenService.GenerateUserTokenAsync(user, permissions);

            // Update last login
            user.LastLogin = DateTime.UtcNow;
            user.FailedAttempts = 0;
            await _userService.UpdateUserAsync(user.UserId, new UpdateUserRequest());

            // Log successful login
            await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, true);

            var userResponse = new UserResponse
            {
                UserId = user.UserId,
                EmployeeId = user.EmployeeId,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                FullName = user.FullName,
                IsActive = user.IsActive,
                LastLogin = user.LastLogin,
                IsLockedOut = user.IsLockedOut,
                CreatedDate = user.CreatedDate,
                Roles = user.UserRoles.Select(ur => new RoleResponse
                {
                    RoleId = ur.Role.RoleId,
                    RoleCode = ur.Role.RoleCode,
                    Name = ur.Role.Name,
                    Description = ur.Role.Description,
                    IsActive = ur.Role.IsActive,
                    AssignedDate = ur.AssignedDate
                }).ToList()
            };

            return Ok(new UserLoginResponse
            {
                AccessToken = accessToken,
                TokenType = "Bearer",
                ExpiresIn = 28800, // 8 hours
                User = userResponse,
                Permissions = permissions
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during user login for {EmailOrEmployeeId}", request.EmailOrEmployeeId);

            // Determine if input is email or employee ID for proper logging
            string? attemptedEmail = request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;
            string? attemptedEmployeeId = !request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;

            await _userService.LogLoginAttemptAsync(null, attemptedEmail, attemptedEmployeeId, ipAddress, false, "Login error");
            return StatusCode(500, new { error = "An error occurred during login" });
        }
    }

    /// <summary>
    /// Validate user token
    /// </summary>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(UserTokenValidationResult), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<UserTokenValidationResult>> ValidateToken([FromBody] ValidateTokenRequest request)
    {
        try
        {
            var result = await _userTokenService.ValidateUserTokenAsync(request.Token);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating user token");
            return BadRequest(new { error = "Token validation failed" });
        }
    }

    /// <summary>
    /// Refresh user token
    /// </summary>
    [HttpPost("refresh")]
    [Authorize]
    [ProducesResponseType(typeof(RefreshTokenResponse), 200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<RefreshTokenResponse>> RefreshToken()
    {
        try
        {
            var userIdClaim = User.FindFirst("sub")?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized(new { error = "Invalid token" });
            }

            var user = await _userService.GetUserAsync(userId);
            if (user == null || !user.IsActive || user.IsLockedOut)
            {
                return Unauthorized(new { error = "User not found or inactive" });
            }

            // Get updated permissions
            var permissions = await _userService.GetUserPermissionsAsync(userId);

            // Generate new token
            var accessToken = await _userTokenService.GenerateUserTokenAsync(user, permissions);

            return Ok(new RefreshTokenResponse
            {
                AccessToken = accessToken,
                TokenType = "Bearer",
                ExpiresIn = 28800 // 8 hours
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing user token");
            return StatusCode(500, new { error = "Token refresh failed" });
        }
    }

    /// <summary>
    /// Logout user (revoke token)
    /// </summary>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(200)]
    public async Task<IActionResult> Logout()
    {
        try
        {
            var jtiClaim = User.FindFirst("jti")?.Value;
            if (!string.IsNullOrEmpty(jtiClaim))
            {
                await _userTokenService.RevokeUserTokenAsync(jtiClaim);
            }

            var userIdClaim = User.FindFirst("sub")?.Value;
            if (Guid.TryParse(userIdClaim, out var userId))
            {
                await _auditService.LogAsync("user_logout", "auth", 
                    $"User logged out: {userId}", null, GetClientIpAddress(), GetUserAgent());
            }

            return Ok(new { message = "Logged out successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return StatusCode(500, new { error = "Logout failed" });
        }
    }

    /// <summary>
    /// Get current user profile
    /// </summary>
    [HttpGet("profile")]
    [Authorize]
    [ProducesResponseType(typeof(UserResponse), 200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<UserResponse>> GetProfile()
    {
        try
        {
            var userIdClaim = User.FindFirst("sub")?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized(new { error = "Invalid token" });
            }

            var user = await _userService.GetUserAsync(userId);
            if (user == null)
            {
                return Unauthorized(new { error = "User not found" });
            }

            var response = new UserResponse
            {
                UserId = user.UserId,
                EmployeeId = user.EmployeeId,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                FullName = user.FullName,
                IsActive = user.IsActive,
                LastLogin = user.LastLogin,
                IsLockedOut = user.IsLockedOut,
                CreatedDate = user.CreatedDate,
                Roles = user.UserRoles.Select(ur => new RoleResponse
                {
                    RoleId = ur.Role.RoleId,
                    RoleCode = ur.Role.RoleCode,
                    Name = ur.Role.Name,
                    Description = ur.Role.Description,
                    IsActive = ur.Role.IsActive,
                    AssignedDate = ur.AssignedDate
                }).ToList()
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user profile");
            return StatusCode(500, new { error = "Failed to get profile" });
        }
    }

    private string GetClientIpAddress()
    {
        return Request.Headers["X-Forwarded-For"].FirstOrDefault() 
               ?? Request.Headers["X-Real-IP"].FirstOrDefault()
               ?? Request.HttpContext.Connection.RemoteIpAddress?.ToString() 
               ?? "unknown";
    }

    private string GetUserAgent()
    {
        return Request.Headers.UserAgent.ToString();
    }

    private async Task<TokenExchangeResult> ExchangeCodeForTokenAsync(string code, string redirectUri, string? codeVerifier)
    {
        try
        {
            using var httpClient = new HttpClient();
            var tokenEndpoint = $"https://login.microsoftonline.com/{_configuration["AzureAd:TenantId"]}/oauth2/v2.0/token";

            var requestParams = new List<KeyValuePair<string, string>>
            {
                new("client_id", _configuration["AzureAd:ClientId"]!),
                new("code", code),
                new("redirect_uri", redirectUri),
                new("grant_type", "authorization_code"),
                new("scope", "openid profile email User.Read")
            };

            // Add PKCE parameters only if code verifier is provided
            if (!string.IsNullOrEmpty(codeVerifier))
            {
                requestParams.Add(new KeyValuePair<string, string>("code_verifier", codeVerifier));
            }
            else
            {
                // For non-PKCE flow, add client secret if available
                var clientSecret = _configuration["AzureAd:ClientSecret"];
                if (!string.IsNullOrEmpty(clientSecret))
                {
                    requestParams.Add(new KeyValuePair<string, string>("client_secret", clientSecret));
                }
            }

            var requestBody = new FormUrlEncodedContent(requestParams);

            var response = await httpClient.PostAsync(tokenEndpoint, requestBody);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Token exchange failed: {Response}", responseContent);
                return new TokenExchangeResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Token exchange failed"
                };
            }

            var tokenResponse = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);
            var accessToken = tokenResponse?["access_token"]?.ToString();

            if (string.IsNullOrEmpty(accessToken))
            {
                return new TokenExchangeResult
                {
                    IsSuccess = false,
                    ErrorMessage = "No access token received"
                };
            }

            return new TokenExchangeResult
            {
                IsSuccess = true,
                AccessToken = accessToken
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exchanging code for token");
            return new TokenExchangeResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Get Azure AD authorization URL for OAuth2 flow with MFA support
    /// </summary>
    [HttpGet("azure-auth-url")]
    public IActionResult GetAzureAuthUrl([FromQuery] string redirectUri = "http://localhost:3000/auth/callback", [FromQuery] bool usePkce = false)
    {
        try
        {
            var state = Guid.NewGuid().ToString();

            var authUrlBuilder = $"https://login.microsoftonline.com/{_configuration["AzureAd:TenantId"]}/oauth2/v2.0/authorize?" +
                               $"client_id={_configuration["AzureAd:ClientId"]}&" +
                               $"response_type=code&" +
                               $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                               $"response_mode=query&" +
                               $"scope={Uri.EscapeDataString("openid profile email User.Read")}&" +
                               $"state={state}";

            if (usePkce)
            {
                var codeVerifier = AuthHelpers.GenerateCodeVerifier();
                var codeChallenge = AuthHelpers.GenerateCodeChallenge(codeVerifier);

                // Store code verifier temporarily
                HttpContext.Session.SetString($"code_verifier_{state}", codeVerifier);

                authUrlBuilder += $"&code_challenge={codeChallenge}&code_challenge_method=S256";
            }
            else
            {
                // Store a flag to indicate no PKCE is used
                HttpContext.Session.SetString($"no_pkce_{state}", "true");
            }

            return Ok(new
            {
                authUrl = authUrlBuilder,
                state = state,
                usePkce = usePkce,
                message = "Redirect user to this URL for Azure AD authentication with MFA support"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating Azure AD auth URL");
            return BadRequest(new { error = "Failed to generate authentication URL" });
        }
    }

    /// <summary>
    /// Handle OAuth2 callback and complete authentication with MFA support
    /// </summary>
    [HttpPost("azure-callback")]
    public async Task<IActionResult> HandleAzureCallback([FromBody] AzureCallbackRequest request)
    {
        try
        {
            var ipAddress = GetClientIpAddress();

            // Check if PKCE was used
            var codeVerifier = HttpContext.Session.GetString($"code_verifier_{request.State}");
            var noPkceFlag = HttpContext.Session.GetString($"no_pkce_{request.State}");

            if (string.IsNullOrEmpty(codeVerifier) && string.IsNullOrEmpty(noPkceFlag))
            {
                return BadRequest(new { error = "Invalid or expired authentication state" });
            }

            // Exchange authorization code for access token
            var tokenResult = await ExchangeCodeForTokenAsync(request.Code, request.RedirectUri, codeVerifier);
            if (!tokenResult.IsSuccess)
            {
                return Unauthorized(new { error = "Failed to exchange authorization code for token" });
            }

            // Get user info from Azure AD
            var azureUser = await _azureAdService.ValidateTokenAsync(tokenResult.AccessToken);
            if (azureUser == null)
            {
                return Unauthorized(new { error = "Failed to get user information from Azure AD" });
            }

            // Find user in local database
            var user = await _userService.GetUserByEmailAsync(azureUser.Email);
            if (user == null)
            {
                _logger.LogWarning("User {Email} not found in local database after Azure AD authentication", azureUser.Email);
                return Unauthorized(new { error = "User not found in local database" });
            }

            // Check if user is active and not locked
            if (!user.IsActive)
            {
                await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, "User account is inactive");
                return Unauthorized(new { error = "User account is inactive" });
            }

            if (user.IsLockedOut)
            {
                await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, "User account is locked");
                return Unauthorized(new { error = "User account is locked" });
            }

            // Successful authentication - generate JWT token
            var permissions = await _userService.GetUserPermissionsAsync(user.UserId);
            var jwtToken = await _userTokenService.GenerateUserTokenAsync(user, permissions);

            // Log successful login
            await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, true, "Successful Azure AD OAuth2 login with MFA");

            // Clean up session
            HttpContext.Session.Remove($"code_verifier_{request.State}");
            HttpContext.Session.Remove($"no_pkce_{request.State}");

            return Ok(new UserLoginResponse
            {
                AccessToken = jwtToken,
                ExpiresIn = 3600,
                User = new UserResponse
                {
                    UserId = user.UserId,
                    EmployeeId = user.EmployeeId,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    FullName = user.FullName,
                    IsActive = user.IsActive,
                    LastLogin = user.LastLogin,
                    IsLockedOut = user.IsLockedOut,
                    CreatedDate = user.CreatedDate,
                    Roles = user.UserRoles.Select(ur => new RoleResponse
                    {
                        RoleId = ur.Role.RoleId,
                        RoleCode = ur.Role.RoleCode,
                        Name = ur.Role.Name,
                        Description = ur.Role.Description
                    }).ToList()
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling Azure AD callback");
            return BadRequest(new { error = "Authentication failed" });
        }
    }

    /// <summary>
    /// Test Azure AD authentication without PKCE (for troubleshooting)
    /// </summary>
    [HttpGet("azure-auth-url-simple")]
    public IActionResult GetSimpleAzureAuthUrl([FromQuery] string redirectUri = "http://localhost:3000/auth/callback")
    {
        try
        {
            var state = Guid.NewGuid().ToString();

            // Store a flag to indicate no PKCE is used
            HttpContext.Session.SetString($"no_pkce_{state}", "true");

            var authUrl = $"https://login.microsoftonline.com/{_configuration["AzureAd:TenantId"]}/oauth2/v2.0/authorize?" +
                         $"client_id={_configuration["AzureAd:ClientId"]}&" +
                         $"response_type=code&" +
                         $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                         $"response_mode=query&" +
                         $"scope={Uri.EscapeDataString("openid profile email User.Read")}&" +
                         $"state={state}";

            return Ok(new
            {
                authUrl = authUrl,
                state = state,
                usePkce = false,
                message = "Simple Azure AD authentication URL without PKCE (for troubleshooting)"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating simple Azure AD auth URL");
            return BadRequest(new { error = "Failed to generate authentication URL" });
        }
    }

    /// <summary>
    /// Check if a user account has MFA or other authentication requirements
    /// </summary>
    [HttpPost("check-auth-requirements")]
    public async Task<IActionResult> CheckAuthRequirements([FromBody] CheckAuthRequirementsRequest request)
    {
        try
        {
            _logger.LogInformation("Checking authentication requirements for {EmailOrEmployeeId}", request.EmailOrEmployeeId);

            // Find user in database first
            User? user = null;
            if (request.EmailOrEmployeeId.Contains("@"))
            {
                user = await _userService.GetUserByEmailAsync(request.EmailOrEmployeeId);
            }
            else
            {
                user = await _userService.GetUserByEmployeeIdAsync(request.EmailOrEmployeeId);
            }

            if (user == null)
            {
                return NotFound(new { error = "User not found in local database" });
            }

            if (!user.IsActive)
            {
                return BadRequest(new { error = "User account is inactive" });
            }

            if (user.IsLockedOut)
            {
                return BadRequest(new { error = "User account is locked" });
            }

            // Try a test authentication with a dummy password to detect MFA requirements
            // This will fail but give us information about authentication requirements
            var testResult = await _azureAdService.AuthenticateUserAsync(user.Email, "dummy_password_for_mfa_check");

            var response = new
            {
                email = user.Email,
                employeeId = user.EmployeeId,
                fullName = user.FullName,
                authenticationMethod = DetermineAuthMethod(testResult),
                requiresMfa = testResult.ErrorCode == "MFA_REQUIRED",
                requiresConditionalAccess = testResult.ErrorCode == "CONDITIONAL_ACCESS_REQUIRED",
                supportsPasswordAuth = testResult.ErrorCode != "MFA_REQUIRED" && testResult.ErrorCode != "CONDITIONAL_ACCESS_REQUIRED",
                recommendedFlow = GetRecommendedAuthFlow(testResult),
                message = GetAuthRequirementMessage(testResult, user.Email)
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication requirements for {EmailOrEmployeeId}", request.EmailOrEmployeeId);
            return StatusCode(500, new { error = "An error occurred while checking authentication requirements" });
        }
    }

    private static string DetermineAuthMethod(AzureAdAuthResult testResult)
    {
        return testResult.ErrorCode switch
        {
            "MFA_REQUIRED" => "OAuth2 with MFA",
            "CONDITIONAL_ACCESS_REQUIRED" => "OAuth2 with Conditional Access",
            "ACCOUNT_DISABLED" => "Account Disabled",
            "PASSWORD_EXPIRED" => "Password Reset Required",
            _ => "Password or OAuth2"
        };
    }

    private static string GetRecommendedAuthFlow(AzureAdAuthResult testResult)
    {
        return testResult.ErrorCode switch
        {
            "MFA_REQUIRED" or "CONDITIONAL_ACCESS_REQUIRED" => "oauth2",
            "ACCOUNT_DISABLED" => "contact_admin",
            "PASSWORD_EXPIRED" => "password_reset",
            _ => "password_or_oauth2"
        };
    }

    private static string GetAuthRequirementMessage(AzureAdAuthResult testResult, string email)
    {
        return testResult.ErrorCode switch
        {
            "MFA_REQUIRED" => $"Account '{email}' has Multi-Factor Authentication (MFA) enabled. Use OAuth2 flow for authentication.",
            "CONDITIONAL_ACCESS_REQUIRED" => $"Account '{email}' is subject to Conditional Access policies. Use OAuth2 flow for authentication.",
            "ACCOUNT_DISABLED" => $"Account '{email}' is disabled in Azure AD. Contact your administrator.",
            "PASSWORD_EXPIRED" => $"Password for account '{email}' has expired. Please reset your password.",
            _ => $"Account '{email}' supports both password and OAuth2 authentication methods."
        };
    }

    /// <summary>
    /// Create a short failure reason for database logging (max 200 characters)
    /// </summary>
    private static string CreateShortFailureReason(AzureAdAuthResult azureAuthResult)
    {
        var reason = azureAuthResult.ErrorCode switch
        {
            "MFA_REQUIRED" => "MFA required - use OAuth2",
            "CONDITIONAL_ACCESS_REQUIRED" => "Conditional Access - use OAuth2",
            "ACCOUNT_DISABLED" => "Account disabled in Azure AD",
            "PASSWORD_EXPIRED" => "Password expired",
            "AZURE_AUTH_FAILED" => "Invalid credentials",
            _ => azureAuthResult.ErrorCode ?? "Authentication failed"
        };

        // Ensure it fits within 200 characters
        return reason.Length > 200 ? reason.Substring(0, 197) + "..." : reason;
    }

    /// <summary>
    /// Create detailed error response with specific field validation
    /// </summary>
    private object CreateDetailedErrorResponse(AzureAdAuthResult azureAuthResult, string emailOrEmployeeId)
    {
        var isEmail = emailOrEmployeeId.Contains("@");

        return azureAuthResult.ErrorCode switch
        {
            "MFA_REQUIRED" => new
            {
                error = "Multi-Factor Authentication (MFA) is required for this account",
                errorCode = "MFA_REQUIRED",
                field = "authentication_method",
                message = $"Account '{emailOrEmployeeId}' has MFA enabled. Please use the OAuth2 authentication flow instead of password authentication.",
                requiresOAuth = true,
                suggestedAction = "Use OAuth2 flow for authentication"
            },
            "CONDITIONAL_ACCESS_REQUIRED" => new
            {
                error = "Conditional Access policies require additional authentication",
                errorCode = "CONDITIONAL_ACCESS_REQUIRED",
                field = "authentication_method",
                message = $"Account '{emailOrEmployeeId}' is subject to Conditional Access policies. Please use the OAuth2 authentication flow.",
                requiresOAuth = true,
                suggestedAction = "Use OAuth2 flow for authentication"
            },
            "ACCOUNT_DISABLED" => new
            {
                error = "Account is disabled",
                errorCode = "ACCOUNT_DISABLED",
                field = isEmail ? "email" : "employeeId",
                message = $"Account '{emailOrEmployeeId}' is disabled in Azure AD. Please contact your administrator.",
                requiresOAuth = false,
                suggestedAction = "Contact administrator"
            },
            "PASSWORD_EXPIRED" => new
            {
                error = "Password has expired",
                errorCode = "PASSWORD_EXPIRED",
                field = "password",
                message = $"Password for account '{emailOrEmployeeId}' has expired. Please reset your password.",
                requiresOAuth = false,
                suggestedAction = "Reset password"
            },
            "AZURE_AUTH_FAILED" => new
            {
                error = "Invalid credentials",
                errorCode = "INVALID_CREDENTIALS",
                field = "password",
                message = $"The password provided for '{emailOrEmployeeId}' is incorrect. Please check your password and try again.",
                requiresOAuth = false,
                suggestedAction = "Check password"
            },
            _ => new
            {
                error = "Authentication failed",
                errorCode = azureAuthResult.ErrorCode ?? "AUTH_FAILED",
                field = "credentials",
                message = azureAuthResult.ErrorMessage ?? $"Authentication failed for '{emailOrEmployeeId}'. Please check your credentials.",
                requiresOAuth = false,
                suggestedAction = "Check credentials"
            }
        };
    }

    /// <summary>
    /// Test endpoint to encrypt a password for testing purposes
    /// </summary>
    [HttpPost("encrypt-password")]
    public IActionResult EncryptPassword([FromBody] EncryptPasswordRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Password))
            {
                return BadRequest(new { error = "Password is required" });
            }

            var encryptedPassword = _cryptoService.EncryptPassword(request.Password);

            return Ok(new
            {
                encryptedPassword = encryptedPassword,
                message = "Password encrypted successfully. Use this encrypted password in login requests."
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error encrypting password");
            return StatusCode(500, new { error = "Failed to encrypt password" });
        }
    }
}

// Additional DTOs
public class ValidateTokenRequest
{
    public string Token { get; set; } = string.Empty;
}

public class RefreshTokenResponse
{
    public string AccessToken { get; set; } = string.Empty;
    public string TokenType { get; set; } = "Bearer";
    public int ExpiresIn { get; set; }
}

public class EncryptPasswordRequest
{
    public string Password { get; set; } = string.Empty;
}

public class CheckAuthRequirementsRequest
{
    [Required]
    public string EmailOrEmployeeId { get; set; } = string.Empty;
}

public class AzureCallbackRequest
{
    public string Code { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string RedirectUri { get; set; } = string.Empty;
}

public class TokenExchangeResult
{
    public bool IsSuccess { get; set; }
    public string AccessToken { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
}

// Helper methods for PKCE and OAuth2 flow
public static class AuthHelpers
{
    public static string GenerateCodeVerifier()
    {
        var bytes = new byte[32];
        using var rng = System.Security.Cryptography.RandomNumberGenerator.Create();
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes)
            .TrimEnd('=')
            .Replace('+', '-')
            .Replace('/', '_');
    }

    public static string GenerateCodeChallenge(string codeVerifier)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var challengeBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(codeVerifier));
        return Convert.ToBase64String(challengeBytes)
            .TrimEnd('=')
            .Replace('+', '-')
            .Replace('/', '_');
    }
}
