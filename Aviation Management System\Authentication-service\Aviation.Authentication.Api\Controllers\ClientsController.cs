using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Aviation.Authentication.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize] // Require authentication for client management
public class ClientsController : ControllerBase
{
    private readonly IClientService _clientService;
    private readonly IScopeService _scopeService;
    private readonly IAuditService _auditService;
    private readonly ILogger<ClientsController> _logger;

    public ClientsController(
        IClientService clientService,
        IScopeService scopeService,
        IAuditService auditService,
        ILogger<ClientsController> logger)
    {
        _clientService = clientService;
        _scopeService = scopeService;
        _auditService = auditService;
        _logger = logger;
    }

    /// <summary>
    /// Get all clients with pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<ClientResponse>), 200)]
    public async Task<ActionResult<IEnumerable<ClientResponse>>> GetClients(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20)
    {
        if (page < 1) page = 1;
        if (pageSize < 1 || pageSize > 100) pageSize = 20;

        var clients = await _clientService.GetClientsAsync(page, pageSize);
        
        var response = clients.Select(c => new ClientResponse
        {
            Id = c.Id,
            ClientId = c.ClientId,
            ClientName = c.ClientName,
            Description = c.Description,
            ClientType = c.ClientType,
            Status = c.Status,
            CreatedAt = c.CreatedAt,
            LastUsedAt = c.LastUsedAt,
            AccessTokenLifetimeSeconds = c.AccessTokenLifetimeSeconds,
            RateLimitPerHour = c.RateLimitPerHour,
            Scopes = c.ClientScopes.Select(cs => cs.Scope.Name).ToList()
        });

        Response.Headers.Add("X-Page", page.ToString());
        Response.Headers.Add("X-Page-Size", pageSize.ToString());

        return Ok(response);
    }

    /// <summary>
    /// Get client by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ClientResponse), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<ClientResponse>> GetClient(int id)
    {
        var client = await _clientService.GetClientByIdAsync(id);
        if (client == null)
        {
            return NotFound($"Client with ID {id} not found");
        }

        var response = new ClientResponse
        {
            Id = client.Id,
            ClientId = client.ClientId,
            ClientName = client.ClientName,
            Description = client.Description,
            ClientType = client.ClientType,
            Status = client.Status,
            CreatedAt = client.CreatedAt,
            LastUsedAt = client.LastUsedAt,
            AccessTokenLifetimeSeconds = client.AccessTokenLifetimeSeconds,
            RateLimitPerHour = client.RateLimitPerHour,
            Scopes = client.ClientScopes.Select(cs => cs.Scope.Name).ToList()
        };

        return Ok(response);
    }

    /// <summary>
    /// Create a new B2B client application
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(CreateClientResponse), 201)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<CreateClientResponse>> CreateClient([FromBody] CreateClientRequest request)
    {
        try
        {
            // Validate scopes exist
            if (!await _scopeService.ValidateScopesAsync(request.Scopes))
            {
                return BadRequest("One or more requested scopes are invalid or inactive");
            }

            var createdBy = User.Identity?.Name ?? "api-admin";
            var response = await _clientService.CreateClientAsync(request, createdBy);

            await _auditService.LogAsync("client_created", "clients", 
                $"New client created: {request.ClientName}", response.Id, GetClientIpAddress(), GetUserAgent());

            return CreatedAtAction(nameof(GetClient), new { id = response.Id }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating client {ClientName}", request.ClientName);
            return StatusCode(500, "An error occurred while creating the client");
        }
    }

    /// <summary>
    /// Update an existing client
    /// </summary>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(ClientResponse), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<ClientResponse>> UpdateClient(int id, [FromBody] UpdateClientRequest request)
    {
        try
        {
            var response = await _clientService.UpdateClientAsync(id, request);
            if (response == null)
            {
                return NotFound($"Client with ID {id} not found");
            }

            await _auditService.LogAsync("client_updated", "clients", 
                $"Client updated: {response.ClientName}", id, GetClientIpAddress(), GetUserAgent());

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating client {ClientId}", id);
            return StatusCode(500, "An error occurred while updating the client");
        }
    }

    /// <summary>
    /// Update client scopes
    /// </summary>
    [HttpPut("{id}/scopes")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> UpdateClientScopes(int id, [FromBody] UpdateClientScopesRequest request)
    {
        try
        {
            // Validate scopes exist
            if (!await _scopeService.ValidateScopesAsync(request.Scopes))
            {
                return BadRequest("One or more requested scopes are invalid or inactive");
            }

            var updatedBy = User.Identity?.Name ?? "api-admin";
            var success = await _clientService.UpdateClientScopesAsync(id, request.Scopes, updatedBy);
            
            if (!success)
            {
                return NotFound($"Client with ID {id} not found");
            }

            await _auditService.LogAsync("client_scopes_updated", "clients", 
                $"Scopes updated for client ID {id}: {string.Join(", ", request.Scopes)}", 
                id, GetClientIpAddress(), GetUserAgent());

            return Ok(new { message = "Client scopes updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating scopes for client {ClientId}", id);
            return StatusCode(500, "An error occurred while updating client scopes");
        }
    }

    /// <summary>
    /// Update client status (activate, suspend, etc.)
    /// </summary>
    [HttpPatch("{id}/status")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> UpdateClientStatus(int id, [FromBody] UpdateClientStatusRequest request)
    {
        try
        {
            var updatedBy = User.Identity?.Name ?? "api-admin";
            var success = await _clientService.UpdateClientStatusAsync(id, request.Status, updatedBy);
            
            if (!success)
            {
                return NotFound($"Client with ID {id} not found");
            }

            await _auditService.LogAsync("client_status_updated", "clients", 
                $"Status updated for client ID {id} to {request.Status}", 
                id, GetClientIpAddress(), GetUserAgent());

            return Ok(new { message = $"Client status updated to {request.Status}" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating status for client {ClientId}", id);
            return StatusCode(500, "An error occurred while updating client status");
        }
    }

    /// <summary>
    /// Delete a client (this will revoke all tokens)
    /// </summary>
    [HttpDelete("{id}")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> DeleteClient(int id)
    {
        try
        {
            var success = await _clientService.DeleteClientAsync(id);
            if (!success)
            {
                return NotFound($"Client with ID {id} not found");
            }

            await _auditService.LogAsync("client_deleted", "clients", 
                $"Client deleted: ID {id}", id, GetClientIpAddress(), GetUserAgent());

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting client {ClientId}", id);
            return StatusCode(500, "An error occurred while deleting the client");
        }
    }

    /// <summary>
    /// Get client statistics
    /// </summary>
    [HttpGet("{id}/stats")]
    [ProducesResponseType(typeof(ClientStatsResponse), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<ClientStatsResponse>> GetClientStats(int id)
    {
        var client = await _clientService.GetClientByIdAsync(id);
        if (client == null)
        {
            return NotFound($"Client with ID {id} not found");
        }

        // This would typically involve querying token usage, API calls, etc.
        // For now, return basic stats
        var stats = new ClientStatsResponse
        {
            ClientId = client.ClientId,
            ClientName = client.ClientName,
            TotalTokensIssued = client.AccessTokens.Count,
            ActiveTokens = client.AccessTokens.Count(t => !t.IsRevoked && t.ExpiresAt > DateTime.UtcNow),
            LastUsed = client.LastUsedAt,
            CreatedAt = client.CreatedAt
        };

        return Ok(stats);
    }

    private string GetClientIpAddress()
    {
        return Request.Headers["X-Forwarded-For"].FirstOrDefault() 
               ?? Request.Headers["X-Real-IP"].FirstOrDefault()
               ?? Request.HttpContext.Connection.RemoteIpAddress?.ToString() 
               ?? "unknown";
    }

    private string GetUserAgent()
    {
        return Request.Headers.UserAgent.ToString();
    }
}

// Additional DTOs
public class UpdateClientScopesRequest
{
    public List<string> Scopes { get; set; } = new();
}

public class UpdateClientStatusRequest
{
    public ClientStatus Status { get; set; }
    public string? Reason { get; set; }
}

public class ClientStatsResponse
{
    public string ClientId { get; set; } = string.Empty;
    public string ClientName { get; set; } = string.Empty;
    public int TotalTokensIssued { get; set; }
    public int ActiveTokens { get; set; }
    public DateTime? LastUsed { get; set; }
    public DateTime CreatedAt { get; set; }
}
