using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Aviation.Authentication.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ScopesController : ControllerBase
{
    private readonly IScopeService _scopeService;
    private readonly IAuditService _auditService;
    private readonly ILogger<ScopesController> _logger;

    public ScopesController(
        IScopeService scopeService,
        IAuditService auditService,
        ILogger<ScopesController> logger)
    {
        _scopeService = scopeService;
        _auditService = auditService;
        _logger = logger;
    }

    /// <summary>
    /// Get all available scopes
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<Scope>), 200)]
    public async Task<ActionResult<IEnumerable<Scope>>> GetScopes()
    {
        var scopes = await _scopeService.GetScopesAsync();
        return Ok(scopes);
    }

    /// <summary>
    /// Get scope by name
    /// </summary>
    [HttpGet("{name}")]
    [ProducesResponseType(typeof(Scope), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<Scope>> GetScope(string name)
    {
        var scope = await _scopeService.GetScopeAsync(name);
        if (scope == null)
        {
            return NotFound($"Scope '{name}' not found");
        }

        return Ok(scope);
    }

    /// <summary>
    /// Create a new scope
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(Scope), 201)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<Scope>> CreateScope([FromBody] CreateScopeRequest request)
    {
        try
        {
            var scope = await _scopeService.CreateScopeAsync(request);

            await _auditService.LogAsync("scope_created", "scopes", 
                $"New scope created: {request.Name}", null, GetClientIpAddress(), GetUserAgent());

            return CreatedAtAction(nameof(GetScope), new { name = scope.Name }, scope);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating scope {ScopeName}", request.Name);
            return StatusCode(500, "An error occurred while creating the scope");
        }
    }

    /// <summary>
    /// Update an existing scope
    /// </summary>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(Scope), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<Scope>> UpdateScope(int id, [FromBody] UpdateScopeRequest request)
    {
        try
        {
            var scope = await _scopeService.UpdateScopeAsync(id, request);
            if (scope == null)
            {
                return NotFound($"Scope with ID {id} not found");
            }

            await _auditService.LogAsync("scope_updated", "scopes", 
                $"Scope updated: {scope.Name}", null, GetClientIpAddress(), GetUserAgent());

            return Ok(scope);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating scope {ScopeId}", id);
            return StatusCode(500, "An error occurred while updating the scope");
        }
    }

    /// <summary>
    /// Delete a scope
    /// </summary>
    [HttpDelete("{id}")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> DeleteScope(int id)
    {
        try
        {
            var success = await _scopeService.DeleteScopeAsync(id);
            if (!success)
            {
                return NotFound($"Scope with ID {id} not found");
            }

            await _auditService.LogAsync("scope_deleted", "scopes", 
                $"Scope deleted: ID {id}", null, GetClientIpAddress(), GetUserAgent());

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting scope {ScopeId}", id);
            return StatusCode(500, "An error occurred while deleting the scope");
        }
    }

    /// <summary>
    /// Get scopes grouped by category
    /// </summary>
    [HttpGet("by-category")]
    [ProducesResponseType(typeof(Dictionary<string, IEnumerable<Scope>>), 200)]
    public async Task<ActionResult<Dictionary<string, IEnumerable<Scope>>>> GetScopesByCategory()
    {
        var scopes = await _scopeService.GetScopesAsync();
        var groupedScopes = scopes
            .GroupBy(s => s.Category.ToString())
            .ToDictionary(g => g.Key, g => g.AsEnumerable());

        return Ok(groupedScopes);
    }

    private string GetClientIpAddress()
    {
        return Request.Headers["X-Forwarded-For"].FirstOrDefault() 
               ?? Request.Headers["X-Real-IP"].FirstOrDefault()
               ?? Request.HttpContext.Connection.RemoteIpAddress?.ToString() 
               ?? "unknown";
    }

    private string GetUserAgent()
    {
        return Request.Headers.UserAgent.ToString();
    }
}
