# Use the official .NET 8 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 8 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Aviation.Authentication.Api/Aviation.Authentication.Api.csproj", "Aviation.Authentication.Api/"]

# Restore dependencies
RUN dotnet restore "Aviation.Authentication.Api/Aviation.Authentication.Api.csproj"

# Copy source code
COPY . .
WORKDIR "/src/Aviation.Authentication.Api"

# Build the application
RUN dotnet build "Aviation.Authentication.Api.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "Aviation.Authentication.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

ENTRYPOINT ["dotnet", "Aviation.Authentication.Api.dll"]
