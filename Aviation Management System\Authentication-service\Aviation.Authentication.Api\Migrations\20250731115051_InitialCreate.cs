using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Aviation.Authentication.Api.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Clients",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ClientId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ClientSecret = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    ClientName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    ClientType = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    LastUsedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    AccessTokenLifetimeSeconds = table.Column<int>(type: "int", nullable: false),
                    RateLimitPerHour = table.Column<int>(type: "int", nullable: false),
                    AllowedIpAddresses = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    WebhookUrl = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    WebhookSecret = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Clients", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Entities",
                columns: table => new
                {
                    EntityId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Entities", x => x.EntityId);
                });

            migrationBuilder.CreateTable(
                name: "Modules",
                columns: table => new
                {
                    ModuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Modules", x => x.ModuleId);
                });

            migrationBuilder.CreateTable(
                name: "Permissions",
                columns: table => new
                {
                    PermissionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Permissions", x => x.PermissionId);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    RoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RoleCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.RoleId);
                });

            migrationBuilder.CreateTable(
                name: "Scopes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DisplayName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Category = table.Column<int>(type: "int", nullable: false),
                    IsRequired = table.Column<bool>(type: "bit", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Scopes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    EmployeeId = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    AzureAdObjectId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FirstName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    LastName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    LastLogin = table.Column<DateTime>(type: "datetime2", nullable: true),
                    FailedAttempts = table.Column<int>(type: "int", nullable: false),
                    LockoutEnd = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.UserId);
                });

            migrationBuilder.CreateTable(
                name: "AccessTokens",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TokenId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ClientId = table.Column<int>(type: "int", nullable: false),
                    TokenHash = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    IssuedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    ExpiresAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsRevoked = table.Column<bool>(type: "bit", nullable: false),
                    RevokedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    RevokedReason = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IpAddress = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    UserAgent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Scopes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccessTokens", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AccessTokens_Clients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "Clients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AuditLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ClientId = table.Column<int>(type: "int", nullable: true),
                    Action = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Resource = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Details = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    IpAddress = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    UserAgent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Success = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AuditLogs_Clients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "Clients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "SubModules",
                columns: table => new
                {
                    SubModuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ModuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubModules", x => x.SubModuleId);
                    table.ForeignKey(
                        name: "FK_SubModules_Modules_ModuleId",
                        column: x => x.ModuleId,
                        principalTable: "Modules",
                        principalColumn: "ModuleId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ClientScopes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ClientId = table.Column<int>(type: "int", nullable: false),
                    ScopeId = table.Column<int>(type: "int", nullable: false),
                    GrantedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    GrantedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClientScopes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ClientScopes_Clients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "Clients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ClientScopes_Scopes_ScopeId",
                        column: x => x.ScopeId,
                        principalTable: "Scopes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "LoginHistory",
                columns: table => new
                {
                    LogId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    AttemptedEmail = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    AttemptedEmployeeId = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    LoginTime = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    IPAddress = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Success = table.Column<bool>(type: "bit", nullable: false),
                    FailureReason = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginHistory", x => x.LogId);
                    table.ForeignKey(
                        name: "FK_LoginHistory_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "PasswordResets",
                columns: table => new
                {
                    RequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Token = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Expires = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsUsed = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PasswordResets", x => x.RequestId);
                    table.ForeignKey(
                        name: "FK_PasswordResets_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserRoles",
                columns: table => new
                {
                    UserRoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AssignedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => x.UserRoleId);
                    table.ForeignKey(
                        name: "FK_UserRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "RoleId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserRoles_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RolePermissions",
                columns: table => new
                {
                    RolePermissionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    EntityId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ModuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    SubModuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    PermissionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Granted = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RolePermissions", x => x.RolePermissionId);
                    table.ForeignKey(
                        name: "FK_RolePermissions_Entities_EntityId",
                        column: x => x.EntityId,
                        principalTable: "Entities",
                        principalColumn: "EntityId");
                    table.ForeignKey(
                        name: "FK_RolePermissions_Modules_ModuleId",
                        column: x => x.ModuleId,
                        principalTable: "Modules",
                        principalColumn: "ModuleId");
                    table.ForeignKey(
                        name: "FK_RolePermissions_Permissions_PermissionId",
                        column: x => x.PermissionId,
                        principalTable: "Permissions",
                        principalColumn: "PermissionId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RolePermissions_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "RoleId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RolePermissions_SubModules_SubModuleId",
                        column: x => x.SubModuleId,
                        principalTable: "SubModules",
                        principalColumn: "SubModuleId");
                });

            migrationBuilder.InsertData(
                table: "Clients",
                columns: new[] { "Id", "AccessTokenLifetimeSeconds", "AllowedIpAddresses", "ClientId", "ClientName", "ClientSecret", "ClientType", "CreatedAt", "CreatedBy", "Description", "LastUsedAt", "RateLimitPerHour", "Status", "UpdatedAt", "WebhookSecret", "WebhookUrl" },
                values: new object[] { 1, 3600, null, "aviation_test_client", "Test Client", "$2a$11$0f.9ilO2oOF5xfVgBF130uo7meUACrw9rUy/JL0Rc3SDQs1FUw8yu", 4, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(2120), "system", "Default test client for development", null, 1000, 0, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(2124), null, null });

            migrationBuilder.InsertData(
                table: "Entities",
                columns: new[] { "EntityId", "CreatedDate", "Description", "Name" },
                values: new object[,]
                {
                    { new Guid("*************-0000-0000-000000000001"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4507), "System users", "Users" },
                    { new Guid("*************-0000-0000-000000000002"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4513), "User roles", "Roles" },
                    { new Guid("*************-0000-0000-000000000003"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4517), "Business partners", "Partners" },
                    { new Guid("*************-0000-0000-000000000004"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4520), "Customer records", "Customers" },
                    { new Guid("*************-0000-0000-000000000005"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4524), "Order records", "Orders" },
                    { new Guid("*************-0000-0000-000000000006"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4528), "Invoice records", "Invoices" },
                    { new Guid("*************-0000-0000-000000000007"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4550), "Product catalog", "Products" },
                    { new Guid("*************-0000-0000-000000000008"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4555), "Document files", "Documents" }
                });

            migrationBuilder.InsertData(
                table: "Modules",
                columns: new[] { "ModuleId", "CreatedDate", "Description", "Name" },
                values: new object[,]
                {
                    { new Guid("*************-0000-0000-000000000001"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4413), "User and role management", "User Management" },
                    { new Guid("*************-0000-0000-000000000002"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4421), "Partner and customer management", "Partner Management" },
                    { new Guid("*************-0000-0000-000000000003"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4426), "Order processing and management", "Order Management" },
                    { new Guid("*************-0000-0000-000000000004"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4430), "Financial operations and billing", "Finance Management" },
                    { new Guid("*************-0000-0000-000000000005"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4435), "Product catalog and pricing", "Product Management" },
                    { new Guid("*************-0000-0000-000000000006"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4439), "Trip estimation and planning", "Trip Management" },
                    { new Guid("*************-0000-0000-000000000007"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4443), "Document storage and management", "Document Management" },
                    { new Guid("*************-0000-0000-000000000008"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4447), "Reporting and analytics", "Reports" }
                });

            migrationBuilder.InsertData(
                table: "Permissions",
                columns: new[] { "PermissionId", "CreatedDate", "Description", "Name" },
                values: new object[,]
                {
                    { new Guid("11111111-1111-1111-1111-111111111111"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4159), "Create new records", "Create" },
                    { new Guid("*************-2222-2222-************"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4165), "View records", "View" },
                    { new Guid("*************-3333-3333-************"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4178), "Modify existing records", "Update" },
                    { new Guid("*************-4444-4444-************"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4183), "Remove records", "Delete" }
                });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "RoleId", "CreatedDate", "Description", "IsActive", "ModifiedDate", "Name", "RoleCode" },
                values: new object[,]
                {
                    { new Guid("********-0000-0000-0000-000000000001"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4290), "Sales department role", true, null, "Sales", "ROLE-001" },
                    { new Guid("********-0000-0000-0000-000000000002"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4295), "Finance department role", true, null, "Finance", "ROLE-002" },
                    { new Guid("********-0000-0000-0000-000000000003"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4300), "Vendor management role", true, null, "Vendor", "ROLE-003" },
                    { new Guid("********-0000-0000-0000-000000000004"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4304), "Supply chain role", true, null, "Supply", "ROLE-004" },
                    { new Guid("********-0000-0000-0000-000000000005"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4312), "Operational role", true, null, "Operational", "ROLE-005" },
                    { new Guid("********-0000-0000-0000-000000000006"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4316), "Non-D operational role", true, null, "Non-D operational", "ROLE-006" },
                    { new Guid("********-0000-0000-0000-000000000007"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4320), "Sales management role", true, null, "Sales Manager", "ROLE-007" },
                    { new Guid("********-0000-0000-0000-000000000008"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4325), "Legal department role", true, null, "Legal", "ROLE-008" },
                    { new Guid("********-0000-0000-0000-************"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4329), "Customer administration role", true, null, "Customer admin", "ROLE-009" },
                    { new Guid("********-0000-0000-0000-************"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4333), "Accounts department role", true, null, "Accounts", "ROLE-010" },
                    { new Guid("********-0000-0000-0000-************"), new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(4338), "Tender committee role", true, null, "Tender Committee", "ROLE-011" }
                });

            migrationBuilder.InsertData(
                table: "Scopes",
                columns: new[] { "Id", "Category", "CreatedAt", "Description", "DisplayName", "IsActive", "IsRequired", "Name" },
                values: new object[,]
                {
                    { 1, 0, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(284), "Read partner information", "Read Partners", true, false, "partner:read" },
                    { 2, 0, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(286), "Create and update partner information", "Write Partners", true, false, "partner:write" },
                    { 3, 1, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(288), "Read customer information", "Read Customers", true, false, "customer:read" },
                    { 4, 1, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(289), "Create and update customer information", "Write Customers", true, false, "customer:write" },
                    { 5, 2, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(290), "Read order information", "Read Orders", true, false, "order:read" },
                    { 6, 2, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(291), "Create and update orders", "Write Orders", true, false, "order:write" },
                    { 7, 2, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(293), "Cancel existing orders", "Cancel Orders", true, false, "order:cancel" },
                    { 8, 3, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(294), "Read invoice information", "Read Invoices", true, false, "invoice:read" },
                    { 9, 3, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(295), "Create and update invoices", "Write Invoices", true, false, "invoice:write" },
                    { 10, 3, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(297), "Read billing information", "Read Billing", true, false, "billing:read" },
                    { 11, 4, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(298), "Read product catalog", "Read Products", true, false, "product:read" },
                    { 12, 4, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(299), "Update product information", "Write Products", true, false, "product:write" },
                    { 13, 4, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(300), "Read pricing information", "Read Pricing", true, false, "pricing:read" },
                    { 14, 4, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(302), "Update pricing information", "Write Pricing", true, false, "pricing:write" },
                    { 15, 5, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(303), "Get trip cost estimates", "Trip Estimation", true, false, "trip:estimate" },
                    { 16, 5, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(304), "Read flight information", "Read Flights", true, false, "flight:read" },
                    { 17, 5, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(305), "Read airport information", "Read Airports", true, false, "airport:read" },
                    { 18, 6, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(307), "Read documents", "Read Documents", true, false, "document:read" },
                    { 19, 6, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(308), "Upload and update documents", "Write Documents", true, false, "document:write" },
                    { 20, 6, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(309), "Delete documents", "Delete Documents", true, false, "document:delete" },
                    { 21, 7, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(310), "Read application information", "Read Applications", true, false, "app:read" },
                    { 22, 7, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(312), "Register and update applications", "Write Applications", true, false, "app:write" },
                    { 23, 7, new DateTime(2025, 7, 31, 11, 50, 46, 717, DateTimeKind.Utc).AddTicks(313), "Administrative access to app registry", "Admin Applications", true, false, "app:admin" }
                });

            migrationBuilder.InsertData(
                table: "ClientScopes",
                columns: new[] { "Id", "ClientId", "GrantedAt", "GrantedBy", "ScopeId" },
                values: new object[,]
                {
                    { 1, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3347), "system", 1 },
                    { 2, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3368), "system", 2 },
                    { 3, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3370), "system", 3 },
                    { 4, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3371), "system", 4 },
                    { 5, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3372), "system", 5 },
                    { 6, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3377), "system", 6 },
                    { 7, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3379), "system", 7 },
                    { 8, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3380), "system", 8 },
                    { 9, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3381), "system", 9 },
                    { 10, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3383), "system", 10 },
                    { 11, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3385), "system", 11 },
                    { 12, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3386), "system", 12 },
                    { 13, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3387), "system", 13 },
                    { 14, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3388), "system", 14 },
                    { 15, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3390), "system", 15 },
                    { 16, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3485), "system", 16 },
                    { 17, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3487), "system", 17 },
                    { 18, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3513), "system", 18 },
                    { 19, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3514), "system", 19 },
                    { 20, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3515), "system", 20 },
                    { 21, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3516), "system", 21 },
                    { 22, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3517), "system", 22 },
                    { 23, 1, new DateTime(2025, 7, 31, 11, 50, 47, 83, DateTimeKind.Utc).AddTicks(3518), "system", 23 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_AccessTokens_ClientId",
                table: "AccessTokens",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_AccessTokens_ExpiresAt",
                table: "AccessTokens",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "IX_AccessTokens_IsRevoked",
                table: "AccessTokens",
                column: "IsRevoked");

            migrationBuilder.CreateIndex(
                name: "IX_AccessTokens_TokenId",
                table: "AccessTokens",
                column: "TokenId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_Action",
                table: "AuditLogs",
                column: "Action");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_ClientId",
                table: "AuditLogs",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_Timestamp",
                table: "AuditLogs",
                column: "Timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_Clients_ClientId",
                table: "Clients",
                column: "ClientId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ClientScopes_ClientId_ScopeId",
                table: "ClientScopes",
                columns: new[] { "ClientId", "ScopeId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ClientScopes_ScopeId",
                table: "ClientScopes",
                column: "ScopeId");

            migrationBuilder.CreateIndex(
                name: "IX_Entities_Name",
                table: "Entities",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LoginHistory_LoginTime",
                table: "LoginHistory",
                column: "LoginTime");

            migrationBuilder.CreateIndex(
                name: "IX_LoginHistory_UserId",
                table: "LoginHistory",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_PasswordResets_Token",
                table: "PasswordResets",
                column: "Token",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PasswordResets_UserId",
                table: "PasswordResets",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Permissions_Name",
                table: "Permissions",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RolePermissions_EntityId",
                table: "RolePermissions",
                column: "EntityId");

            migrationBuilder.CreateIndex(
                name: "IX_RolePermissions_ModuleId",
                table: "RolePermissions",
                column: "ModuleId");

            migrationBuilder.CreateIndex(
                name: "IX_RolePermissions_PermissionId",
                table: "RolePermissions",
                column: "PermissionId");

            migrationBuilder.CreateIndex(
                name: "IX_RolePermissions_RoleId",
                table: "RolePermissions",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_RolePermissions_SubModuleId",
                table: "RolePermissions",
                column: "SubModuleId");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_Name",
                table: "Roles",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Roles_RoleCode",
                table: "Roles",
                column: "RoleCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Scopes_Name",
                table: "Scopes",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubModules_ModuleId_Name",
                table: "SubModules",
                columns: new[] { "ModuleId", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                table: "UserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_UserId_RoleId",
                table: "UserRoles",
                columns: new[] { "UserId", "RoleId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_AzureAdObjectId",
                table: "Users",
                column: "AzureAdObjectId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                table: "Users",
                column: "Email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_EmployeeId",
                table: "Users",
                column: "EmployeeId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AccessTokens");

            migrationBuilder.DropTable(
                name: "AuditLogs");

            migrationBuilder.DropTable(
                name: "ClientScopes");

            migrationBuilder.DropTable(
                name: "LoginHistory");

            migrationBuilder.DropTable(
                name: "PasswordResets");

            migrationBuilder.DropTable(
                name: "RolePermissions");

            migrationBuilder.DropTable(
                name: "UserRoles");

            migrationBuilder.DropTable(
                name: "Clients");

            migrationBuilder.DropTable(
                name: "Scopes");

            migrationBuilder.DropTable(
                name: "Entities");

            migrationBuilder.DropTable(
                name: "Permissions");

            migrationBuilder.DropTable(
                name: "SubModules");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "Modules");
        }
    }
}
