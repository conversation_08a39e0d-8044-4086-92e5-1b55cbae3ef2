﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Aviation.Authentication.Api.Migrations
{
    /// <inheritdoc />
    public partial class AddPasswordColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LoginHistories_Users_UserId",
                table: "LoginHistories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LoginHistories",
                table: "LoginHistories");

            migrationBuilder.RenameTable(
                name: "LoginHistories",
                newName: "LoginHistory");

            migrationBuilder.RenameIndex(
                name: "IX_LoginHistories_UserId",
                table: "LoginHistory",
                newName: "IX_LoginHistory_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_LoginHistories_LoginTime",
                table: "LoginHistory",
                newName: "IX_LoginHistory_LoginTime");

            migrationBuilder.AddColumn<string>(
                name: "Password",
                table: "Users",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_LoginHistory",
                table: "LoginHistory",
                column: "LogId");

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 1,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3701));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 2,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3723));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 3,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3726));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 4,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3727));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 5,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3728));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 6,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3735));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 7,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3736));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 8,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3738));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 9,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3739));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 10,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3741));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 11,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3743));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 12,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3744));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 13,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3746));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 14,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3747));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 15,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3748));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 16,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3750));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 17,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3751));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 18,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3773));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 19,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3774));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 20,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3776));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 21,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3777));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 22,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3778));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 23,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3780));

            migrationBuilder.UpdateData(
                table: "Clients",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ClientSecret", "CreatedAt", "UpdatedAt" },
                values: new object[] { "$2a$11$oGyzJezvovSL4izdbezeG.oUhn29Z88h9aIIqgXuMG4XTZJFOYCC6", new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(2364), new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(2371) });

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4758));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4764));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4769));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4778));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4783));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4788));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4818));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4823));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4607));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4613));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4618));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4627));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4632));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4636));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4641));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4682));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("11111111-1111-1111-1111-111111111111"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4316));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-2222-2222-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4322));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-3333-3333-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4351));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-4444-4444-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4357));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4465));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4471));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4476));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4481));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4487));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4493));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4501));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4506));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000009"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4511));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000010"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4517));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000011"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4522));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2846));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2870));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2873));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2875));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2877));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2879));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2881));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2883));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2885));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2887));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2889));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2891));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2894));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2896));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2898));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2900));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 17,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2902));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 18,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2904));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 19,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2906));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 20,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2908));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 21,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2909));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 22,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2911));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 23,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2913));

            migrationBuilder.AddForeignKey(
                name: "FK_LoginHistory_Users_UserId",
                table: "LoginHistory",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "UserId",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LoginHistory_Users_UserId",
                table: "LoginHistory");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LoginHistory",
                table: "LoginHistory");

            migrationBuilder.DropColumn(
                name: "Password",
                table: "Users");

            migrationBuilder.RenameTable(
                name: "LoginHistory",
                newName: "LoginHistories");

            migrationBuilder.RenameIndex(
                name: "IX_LoginHistory_UserId",
                table: "LoginHistories",
                newName: "IX_LoginHistories_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_LoginHistory_LoginTime",
                table: "LoginHistories",
                newName: "IX_LoginHistories_LoginTime");

            migrationBuilder.AddPrimaryKey(
                name: "PK_LoginHistories",
                table: "LoginHistories",
                column: "LogId");

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 1,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8112));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 2,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8138));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 3,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8141));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 4,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8142));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 5,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8143));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 6,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8156));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 7,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8158));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 8,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8159));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 9,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8161));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 10,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8163));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 11,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8165));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 12,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8166));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 13,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8167));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 14,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8168));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 15,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8168));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 16,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8169));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 17,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8170));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 18,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8187));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 19,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8188));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 20,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8188));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 21,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8189));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 22,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8190));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 23,
                column: "GrantedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8192));

            migrationBuilder.UpdateData(
                table: "Clients",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ClientSecret", "CreatedAt", "UpdatedAt" },
                values: new object[] { "$2a$11$Uy8nEh.wI0yrS5D.XmFUguy7rZW142sekaqihXd0LPv3wD4oFAlgS", new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(7119), new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(7125) });

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8961));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8963));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8966));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8968));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8971));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8973));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8988));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8992));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8904));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8909));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8912));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8914));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8917));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8919));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8921));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8926));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("11111111-1111-1111-1111-111111111111"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8701));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-2222-2222-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8705));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-3333-3333-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8707));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-4444-4444-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8709));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8819));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8822));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8836));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8839));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8842));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8844));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8847));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8850));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000009"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8852));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000010"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8855));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000011"),
                column: "CreatedDate",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 832, DateTimeKind.Utc).AddTicks(8859));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5779));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5784));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5785));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5787));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5788));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5790));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5791));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5792));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5794));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5795));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5796));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5797));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5799));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5800));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5801));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5802));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 17,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5804));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 18,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5805));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 19,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5806));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 20,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5807));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 21,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5808));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 22,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5810));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 23,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 30, 11, 42, 53, 577, DateTimeKind.Utc).AddTicks(5811));

            migrationBuilder.AddForeignKey(
                name: "FK_LoginHistories_Users_UserId",
                table: "LoginHistories",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "UserId",
                onDelete: ReferentialAction.SetNull);
        }
    }
}
