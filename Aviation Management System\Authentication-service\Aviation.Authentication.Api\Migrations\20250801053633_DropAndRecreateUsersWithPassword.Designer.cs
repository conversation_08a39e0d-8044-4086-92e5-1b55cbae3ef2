﻿// <auto-generated />
using System;
using Aviation.Authentication.Api.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Aviation.Authentication.Api.Migrations
{
    [DbContext(typeof(AuthDbContext))]
    [Migration("20250801053633_DropAndRecreateUsersWithPassword")]
    partial class DropAndRecreateUsersWithPassword
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Aviation.Authentication.Api.Models.AccessToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("bit");

                    b.Property<DateTime>("IssuedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("RevokedReason")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Scopes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("TokenHash")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("TokenId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("ExpiresAt");

                    b.HasIndex("IsRevoked");

                    b.HasIndex("TokenId")
                        .IsUnique();

                    b.ToTable("AccessTokens");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.AuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("Details")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Resource")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("Success")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Timestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("Action");

                    b.HasIndex("ClientId");

                    b.HasIndex("Timestamp");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessTokenLifetimeSeconds")
                        .HasColumnType("int");

                    b.Property<string>("AllowedIpAddresses")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ClientName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ClientSecret")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("ClientType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("RateLimitPerHour")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("WebhookSecret")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("WebhookUrl")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId")
                        .IsUnique();

                    b.ToTable("Clients");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AccessTokenLifetimeSeconds = 3600,
                            ClientId = "aviation_test_client",
                            ClientName = "Test Client",
                            ClientSecret = "$2a$11$3cFmyZtV/ABCkL4VH4wCDeH4twxapHrhEzaFXRmfnX1ebxmIVjtg.",
                            ClientType = 4,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(5007),
                            CreatedBy = "system",
                            Description = "Default test client for development",
                            RateLimitPerHour = 1000,
                            Status = 0,
                            UpdatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(5013)
                        });
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.ClientScope", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("GrantedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("GrantedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ScopeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ScopeId");

                    b.HasIndex("ClientId", "ScopeId")
                        .IsUnique();

                    b.ToTable("ClientScopes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6170),
                            GrantedBy = "system",
                            ScopeId = 1
                        },
                        new
                        {
                            Id = 2,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6199),
                            GrantedBy = "system",
                            ScopeId = 2
                        },
                        new
                        {
                            Id = 3,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6202),
                            GrantedBy = "system",
                            ScopeId = 3
                        },
                        new
                        {
                            Id = 4,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6203),
                            GrantedBy = "system",
                            ScopeId = 4
                        },
                        new
                        {
                            Id = 5,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6205),
                            GrantedBy = "system",
                            ScopeId = 5
                        },
                        new
                        {
                            Id = 6,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6212),
                            GrantedBy = "system",
                            ScopeId = 6
                        },
                        new
                        {
                            Id = 7,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6213),
                            GrantedBy = "system",
                            ScopeId = 7
                        },
                        new
                        {
                            Id = 8,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6215),
                            GrantedBy = "system",
                            ScopeId = 8
                        },
                        new
                        {
                            Id = 9,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6216),
                            GrantedBy = "system",
                            ScopeId = 9
                        },
                        new
                        {
                            Id = 10,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6220),
                            GrantedBy = "system",
                            ScopeId = 10
                        },
                        new
                        {
                            Id = 11,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6221),
                            GrantedBy = "system",
                            ScopeId = 11
                        },
                        new
                        {
                            Id = 12,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6223),
                            GrantedBy = "system",
                            ScopeId = 12
                        },
                        new
                        {
                            Id = 13,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6224),
                            GrantedBy = "system",
                            ScopeId = 13
                        },
                        new
                        {
                            Id = 14,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6226),
                            GrantedBy = "system",
                            ScopeId = 14
                        },
                        new
                        {
                            Id = 15,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6227),
                            GrantedBy = "system",
                            ScopeId = 15
                        },
                        new
                        {
                            Id = 16,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6229),
                            GrantedBy = "system",
                            ScopeId = 16
                        },
                        new
                        {
                            Id = 17,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6230),
                            GrantedBy = "system",
                            ScopeId = 17
                        },
                        new
                        {
                            Id = 18,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6242),
                            GrantedBy = "system",
                            ScopeId = 18
                        },
                        new
                        {
                            Id = 19,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6243),
                            GrantedBy = "system",
                            ScopeId = 19
                        },
                        new
                        {
                            Id = 20,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6245),
                            GrantedBy = "system",
                            ScopeId = 20
                        },
                        new
                        {
                            Id = 21,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6246),
                            GrantedBy = "system",
                            ScopeId = 21
                        },
                        new
                        {
                            Id = 22,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6248),
                            GrantedBy = "system",
                            ScopeId = 22
                        },
                        new
                        {
                            Id = 23,
                            ClientId = 1,
                            GrantedAt = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6249),
                            GrantedBy = "system",
                            ScopeId = 23
                        });
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Entity", b =>
                {
                    b.Property<Guid>("EntityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("EntityId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Entities");

                    b.HasData(
                        new
                        {
                            EntityId = new Guid("*************-0000-0000-000000000001"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7301),
                            Description = "System users",
                            Name = "Users"
                        },
                        new
                        {
                            EntityId = new Guid("*************-0000-0000-000000000002"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7306),
                            Description = "User roles",
                            Name = "Roles"
                        },
                        new
                        {
                            EntityId = new Guid("*************-0000-0000-000000000003"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7310),
                            Description = "Business partners",
                            Name = "Partners"
                        },
                        new
                        {
                            EntityId = new Guid("*************-0000-0000-000000000004"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7314),
                            Description = "Customer records",
                            Name = "Customers"
                        },
                        new
                        {
                            EntityId = new Guid("*************-0000-0000-000000000005"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7317),
                            Description = "Order records",
                            Name = "Orders"
                        },
                        new
                        {
                            EntityId = new Guid("*************-0000-0000-000000000006"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7321),
                            Description = "Invoice records",
                            Name = "Invoices"
                        },
                        new
                        {
                            EntityId = new Guid("*************-0000-0000-000000000007"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7350),
                            Description = "Product catalog",
                            Name = "Products"
                        },
                        new
                        {
                            EntityId = new Guid("*************-0000-0000-000000000008"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7354),
                            Description = "Document files",
                            Name = "Documents"
                        });
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.LoginHistory", b =>
                {
                    b.Property<Guid>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AttemptedEmail")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("AttemptedEmployeeId")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LoginTime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<bool>("Success")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("LogId");

                    b.HasIndex("LoginTime");

                    b.HasIndex("UserId");

                    b.ToTable("LoginHistory", (string)null);
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Module", b =>
                {
                    b.Property<Guid>("ModuleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("ModuleId");

                    b.ToTable("Modules");

                    b.HasData(
                        new
                        {
                            ModuleId = new Guid("*************-0000-0000-000000000001"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7170),
                            Description = "User and role management",
                            Name = "User Management"
                        },
                        new
                        {
                            ModuleId = new Guid("*************-0000-0000-000000000002"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7176),
                            Description = "Partner and customer management",
                            Name = "Partner Management"
                        },
                        new
                        {
                            ModuleId = new Guid("*************-0000-0000-000000000003"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7181),
                            Description = "Order processing and management",
                            Name = "Order Management"
                        },
                        new
                        {
                            ModuleId = new Guid("*************-0000-0000-000000000004"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7186),
                            Description = "Financial operations and billing",
                            Name = "Finance Management"
                        },
                        new
                        {
                            ModuleId = new Guid("*************-0000-0000-000000000005"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7191),
                            Description = "Product catalog and pricing",
                            Name = "Product Management"
                        },
                        new
                        {
                            ModuleId = new Guid("*************-0000-0000-000000000006"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7195),
                            Description = "Trip estimation and planning",
                            Name = "Trip Management"
                        },
                        new
                        {
                            ModuleId = new Guid("*************-0000-0000-000000000007"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7203),
                            Description = "Document storage and management",
                            Name = "Document Management"
                        },
                        new
                        {
                            ModuleId = new Guid("*************-0000-0000-000000000008"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7207),
                            Description = "Reporting and analytics",
                            Name = "Reports"
                        });
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.PasswordReset", b =>
                {
                    b.Property<Guid>("RequestId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("bit");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("RequestId");

                    b.HasIndex("Token")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("PasswordResets");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Permission", b =>
                {
                    b.Property<Guid>("PermissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("PermissionId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Permissions");

                    b.HasData(
                        new
                        {
                            PermissionId = new Guid("11111111-1111-1111-1111-111111111111"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6848),
                            Description = "Create new records",
                            Name = "Create"
                        },
                        new
                        {
                            PermissionId = new Guid("*************-2222-2222-************"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6855),
                            Description = "View records",
                            Name = "View"
                        },
                        new
                        {
                            PermissionId = new Guid("*************-3333-3333-************"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6865),
                            Description = "Modify existing records",
                            Name = "Update"
                        },
                        new
                        {
                            PermissionId = new Guid("*************-4444-4444-************"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6869),
                            Description = "Remove records",
                            Name = "Delete"
                        });
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Role", b =>
                {
                    b.Property<Guid>("RoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("RoleCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("RoleId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("RoleCode")
                        .IsUnique();

                    b.ToTable("Roles");

                    b.HasData(
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000001"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6999),
                            Description = "Sales department role",
                            IsActive = true,
                            Name = "Sales",
                            RoleCode = "ROLE-001"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000002"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7017),
                            Description = "Finance department role",
                            IsActive = true,
                            Name = "Finance",
                            RoleCode = "ROLE-002"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000003"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7022),
                            Description = "Vendor management role",
                            IsActive = true,
                            Name = "Vendor",
                            RoleCode = "ROLE-003"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000004"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7027),
                            Description = "Supply chain role",
                            IsActive = true,
                            Name = "Supply",
                            RoleCode = "ROLE-004"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000005"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7032),
                            Description = "Operational role",
                            IsActive = true,
                            Name = "Operational",
                            RoleCode = "ROLE-005"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000006"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7037),
                            Description = "Non-D operational role",
                            IsActive = true,
                            Name = "Non-D operational",
                            RoleCode = "ROLE-006"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7041),
                            Description = "Sales management role",
                            IsActive = true,
                            Name = "Sales Manager",
                            RoleCode = "ROLE-007"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000008"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7046),
                            Description = "Legal department role",
                            IsActive = true,
                            Name = "Legal",
                            RoleCode = "ROLE-008"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-000000000009"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7051),
                            Description = "Customer administration role",
                            IsActive = true,
                            Name = "Customer admin",
                            RoleCode = "ROLE-009"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-************"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7060),
                            Description = "Accounts department role",
                            IsActive = true,
                            Name = "Accounts",
                            RoleCode = "ROLE-010"
                        },
                        new
                        {
                            RoleId = new Guid("********-0000-0000-0000-************"),
                            CreatedDate = new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7065),
                            Description = "Tender committee role",
                            IsActive = true,
                            Name = "Tender Committee",
                            RoleCode = "ROLE-011"
                        });
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.RolePermission", b =>
                {
                    b.Property<Guid>("RolePermissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<Guid?>("EntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Granted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SubModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("RolePermissionId");

                    b.HasIndex("EntityId");

                    b.HasIndex("ModuleId");

                    b.HasIndex("PermissionId");

                    b.HasIndex("RoleId");

                    b.HasIndex("SubModuleId");

                    b.ToTable("RolePermissions");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Scope", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("Category")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Scopes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Category = 0,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(5981),
                            Description = "Read partner information",
                            DisplayName = "Read Partners",
                            IsActive = true,
                            IsRequired = false,
                            Name = "partner:read"
                        },
                        new
                        {
                            Id = 2,
                            Category = 0,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(5988),
                            Description = "Create and update partner information",
                            DisplayName = "Write Partners",
                            IsActive = true,
                            IsRequired = false,
                            Name = "partner:write"
                        },
                        new
                        {
                            Id = 3,
                            Category = 1,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6006),
                            Description = "Read customer information",
                            DisplayName = "Read Customers",
                            IsActive = true,
                            IsRequired = false,
                            Name = "customer:read"
                        },
                        new
                        {
                            Id = 4,
                            Category = 1,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6009),
                            Description = "Create and update customer information",
                            DisplayName = "Write Customers",
                            IsActive = true,
                            IsRequired = false,
                            Name = "customer:write"
                        },
                        new
                        {
                            Id = 5,
                            Category = 2,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6011),
                            Description = "Read order information",
                            DisplayName = "Read Orders",
                            IsActive = true,
                            IsRequired = false,
                            Name = "order:read"
                        },
                        new
                        {
                            Id = 6,
                            Category = 2,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6013),
                            Description = "Create and update orders",
                            DisplayName = "Write Orders",
                            IsActive = true,
                            IsRequired = false,
                            Name = "order:write"
                        },
                        new
                        {
                            Id = 7,
                            Category = 2,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6016),
                            Description = "Cancel existing orders",
                            DisplayName = "Cancel Orders",
                            IsActive = true,
                            IsRequired = false,
                            Name = "order:cancel"
                        },
                        new
                        {
                            Id = 8,
                            Category = 3,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6018),
                            Description = "Read invoice information",
                            DisplayName = "Read Invoices",
                            IsActive = true,
                            IsRequired = false,
                            Name = "invoice:read"
                        },
                        new
                        {
                            Id = 9,
                            Category = 3,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6021),
                            Description = "Create and update invoices",
                            DisplayName = "Write Invoices",
                            IsActive = true,
                            IsRequired = false,
                            Name = "invoice:write"
                        },
                        new
                        {
                            Id = 10,
                            Category = 3,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6024),
                            Description = "Read billing information",
                            DisplayName = "Read Billing",
                            IsActive = true,
                            IsRequired = false,
                            Name = "billing:read"
                        },
                        new
                        {
                            Id = 11,
                            Category = 4,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6026),
                            Description = "Read product catalog",
                            DisplayName = "Read Products",
                            IsActive = true,
                            IsRequired = false,
                            Name = "product:read"
                        },
                        new
                        {
                            Id = 12,
                            Category = 4,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6029),
                            Description = "Update product information",
                            DisplayName = "Write Products",
                            IsActive = true,
                            IsRequired = false,
                            Name = "product:write"
                        },
                        new
                        {
                            Id = 13,
                            Category = 4,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6031),
                            Description = "Read pricing information",
                            DisplayName = "Read Pricing",
                            IsActive = true,
                            IsRequired = false,
                            Name = "pricing:read"
                        },
                        new
                        {
                            Id = 14,
                            Category = 4,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6033),
                            Description = "Update pricing information",
                            DisplayName = "Write Pricing",
                            IsActive = true,
                            IsRequired = false,
                            Name = "pricing:write"
                        },
                        new
                        {
                            Id = 15,
                            Category = 5,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6036),
                            Description = "Get trip cost estimates",
                            DisplayName = "Trip Estimation",
                            IsActive = true,
                            IsRequired = false,
                            Name = "trip:estimate"
                        },
                        new
                        {
                            Id = 16,
                            Category = 5,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6039),
                            Description = "Read flight information",
                            DisplayName = "Read Flights",
                            IsActive = true,
                            IsRequired = false,
                            Name = "flight:read"
                        },
                        new
                        {
                            Id = 17,
                            Category = 5,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6041),
                            Description = "Read airport information",
                            DisplayName = "Read Airports",
                            IsActive = true,
                            IsRequired = false,
                            Name = "airport:read"
                        },
                        new
                        {
                            Id = 18,
                            Category = 6,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6044),
                            Description = "Read documents",
                            DisplayName = "Read Documents",
                            IsActive = true,
                            IsRequired = false,
                            Name = "document:read"
                        },
                        new
                        {
                            Id = 19,
                            Category = 6,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6047),
                            Description = "Upload and update documents",
                            DisplayName = "Write Documents",
                            IsActive = true,
                            IsRequired = false,
                            Name = "document:write"
                        },
                        new
                        {
                            Id = 20,
                            Category = 6,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6049),
                            Description = "Delete documents",
                            DisplayName = "Delete Documents",
                            IsActive = true,
                            IsRequired = false,
                            Name = "document:delete"
                        },
                        new
                        {
                            Id = 21,
                            Category = 7,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6052),
                            Description = "Read application information",
                            DisplayName = "Read Applications",
                            IsActive = true,
                            IsRequired = false,
                            Name = "app:read"
                        },
                        new
                        {
                            Id = 22,
                            Category = 7,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6055),
                            Description = "Register and update applications",
                            DisplayName = "Write Applications",
                            IsActive = true,
                            IsRequired = false,
                            Name = "app:write"
                        },
                        new
                        {
                            Id = 23,
                            Category = 7,
                            CreatedAt = new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6057),
                            Description = "Administrative access to app registry",
                            DisplayName = "Admin Applications",
                            IsActive = true,
                            IsRequired = false,
                            Name = "app:admin"
                        });
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.SubModule", b =>
                {
                    b.Property<Guid>("SubModuleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid>("ModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("SubModuleId");

                    b.HasIndex("ModuleId", "Name")
                        .IsUnique();

                    b.ToTable("SubModules");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.User", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AzureAdObjectId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("FailedAttempts")
                        .HasColumnType("int");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("LockoutEnd")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Password")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("UserId");

                    b.HasIndex("AzureAdObjectId")
                        .IsUnique();

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.UserRole", b =>
                {
                    b.Property<Guid>("UserRoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("AssignedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserRoleId");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId", "RoleId")
                        .IsUnique();

                    b.ToTable("UserRoles");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.AccessToken", b =>
                {
                    b.HasOne("Aviation.Authentication.Api.Models.Client", "Client")
                        .WithMany("AccessTokens")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.AuditLog", b =>
                {
                    b.HasOne("Aviation.Authentication.Api.Models.Client", "Client")
                        .WithMany("AuditLogs")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Client");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.ClientScope", b =>
                {
                    b.HasOne("Aviation.Authentication.Api.Models.Client", "Client")
                        .WithMany("ClientScopes")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Aviation.Authentication.Api.Models.Scope", "Scope")
                        .WithMany("ClientScopes")
                        .HasForeignKey("ScopeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("Scope");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.LoginHistory", b =>
                {
                    b.HasOne("Aviation.Authentication.Api.Models.User", "User")
                        .WithMany("LoginHistories")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.PasswordReset", b =>
                {
                    b.HasOne("Aviation.Authentication.Api.Models.User", "User")
                        .WithMany("PasswordResets")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.RolePermission", b =>
                {
                    b.HasOne("Aviation.Authentication.Api.Models.Entity", "Entity")
                        .WithMany("RolePermissions")
                        .HasForeignKey("EntityId");

                    b.HasOne("Aviation.Authentication.Api.Models.Module", "Module")
                        .WithMany("RolePermissions")
                        .HasForeignKey("ModuleId");

                    b.HasOne("Aviation.Authentication.Api.Models.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Aviation.Authentication.Api.Models.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Aviation.Authentication.Api.Models.SubModule", "SubModule")
                        .WithMany("RolePermissions")
                        .HasForeignKey("SubModuleId");

                    b.Navigation("Entity");

                    b.Navigation("Module");

                    b.Navigation("Permission");

                    b.Navigation("Role");

                    b.Navigation("SubModule");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.SubModule", b =>
                {
                    b.HasOne("Aviation.Authentication.Api.Models.Module", "Module")
                        .WithMany("SubModules")
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Module");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.UserRole", b =>
                {
                    b.HasOne("Aviation.Authentication.Api.Models.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Aviation.Authentication.Api.Models.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Client", b =>
                {
                    b.Navigation("AccessTokens");

                    b.Navigation("AuditLogs");

                    b.Navigation("ClientScopes");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Entity", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Module", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("SubModules");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Permission", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Role", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.Scope", b =>
                {
                    b.Navigation("ClientScopes");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.SubModule", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("Aviation.Authentication.Api.Models.User", b =>
                {
                    b.Navigation("LoginHistories");

                    b.Navigation("PasswordResets");

                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
