﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Aviation.Authentication.Api.Migrations
{
    /// <inheritdoc />
    public partial class DropAndRecreateUsersWithPassword : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 1,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6170));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 2,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6199));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 3,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6202));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 4,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6203));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 5,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6205));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 6,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6212));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 7,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6213));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 8,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6215));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 9,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6216));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 10,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6220));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 11,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6221));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 12,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6223));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 13,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6224));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 14,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6226));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 15,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6227));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 16,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6229));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 17,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6230));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 18,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6242));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 19,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6243));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 20,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6245));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 21,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6246));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 22,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6248));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 23,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6249));

            migrationBuilder.UpdateData(
                table: "Clients",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ClientSecret", "CreatedAt", "UpdatedAt" },
                values: new object[] { "$2a$11$3cFmyZtV/ABCkL4VH4wCDeH4twxapHrhEzaFXRmfnX1ebxmIVjtg.", new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(5007), new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(5013) });

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7301));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7306));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7310));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7314));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7317));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7321));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7350));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7354));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7170));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7176));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7181));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7186));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7191));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7195));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7203));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7207));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("11111111-1111-1111-1111-111111111111"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6848));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-2222-2222-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6855));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-3333-3333-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6865));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-4444-4444-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6869));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(6999));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7017));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7022));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7027));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7032));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7037));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7041));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7046));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000009"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7051));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000010"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7060));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000011"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 219, DateTimeKind.Utc).AddTicks(7065));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(5981));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(5988));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6006));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6009));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6011));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6013));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6016));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6018));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6021));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6024));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6026));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6029));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6031));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6033));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6036));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6039));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 17,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6041));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 18,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6044));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 19,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6047));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 20,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6049));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 21,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6052));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 22,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6055));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 23,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 36, 32, 14, DateTimeKind.Utc).AddTicks(6057));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 1,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3701));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 2,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3723));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 3,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3726));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 4,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3727));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 5,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3728));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 6,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3735));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 7,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3736));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 8,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3738));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 9,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3739));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 10,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3741));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 11,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3743));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 12,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3744));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 13,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3746));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 14,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3747));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 15,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3748));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 16,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3750));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 17,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3751));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 18,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3773));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 19,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3774));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 20,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3776));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 21,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3777));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 22,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3778));

            migrationBuilder.UpdateData(
                table: "ClientScopes",
                keyColumn: "Id",
                keyValue: 23,
                column: "GrantedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(3780));

            migrationBuilder.UpdateData(
                table: "Clients",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ClientSecret", "CreatedAt", "UpdatedAt" },
                values: new object[] { "$2a$11$oGyzJezvovSL4izdbezeG.oUhn29Z88h9aIIqgXuMG4XTZJFOYCC6", new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(2364), new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(2371) });

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4758));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4764));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4769));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4778));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4783));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4788));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4818));

            migrationBuilder.UpdateData(
                table: "Entities",
                keyColumn: "EntityId",
                keyValue: new Guid("*************-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4823));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4607));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4613));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4618));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4627));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4632));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4636));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4641));

            migrationBuilder.UpdateData(
                table: "Modules",
                keyColumn: "ModuleId",
                keyValue: new Guid("*************-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4682));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("11111111-1111-1111-1111-111111111111"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4316));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-2222-2222-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4322));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-3333-3333-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4351));

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "PermissionId",
                keyValue: new Guid("*************-4444-4444-************"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4357));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000001"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4465));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000002"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4471));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000003"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4476));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000004"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4481));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000005"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4487));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000006"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4493));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000007"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4501));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000008"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4506));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000009"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4511));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000010"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4517));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: new Guid("10000000-0000-0000-0000-000000000011"),
                column: "CreatedDate",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 724, DateTimeKind.Utc).AddTicks(4522));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2846));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2870));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2873));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2875));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2877));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2879));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2881));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2883));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2885));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2887));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2889));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2891));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2894));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2896));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2898));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2900));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 17,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2902));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 18,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2904));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 19,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2906));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 20,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2908));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 21,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2909));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 22,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2911));

            migrationBuilder.UpdateData(
                table: "Scopes",
                keyColumn: "Id",
                keyValue: 23,
                column: "CreatedAt",
                value: new DateTime(2025, 8, 1, 5, 31, 19, 520, DateTimeKind.Utc).AddTicks(2913));
        }
    }
}
