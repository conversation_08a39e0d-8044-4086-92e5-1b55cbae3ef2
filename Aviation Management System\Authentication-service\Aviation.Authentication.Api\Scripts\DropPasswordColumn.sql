-- Drop Password column from Users table
-- This script removes the Password column that was previously added

USE [AviationAuthentication]
GO

-- Check if Password column exists before dropping
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'Password')
BEGIN
    -- Drop Password column
    ALTER TABLE [dbo].[Users]
    DROP COLUMN [Password];
    
    PRINT 'Password column dropped successfully from Users table.';
END
ELSE
BEGIN
    PRINT 'Password column does not exist in Users table.';
END
GO

-- Verify the column was dropped
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users' 
ORDER BY ORDINAL_POSITION;

PRINT 'Current Users table structure shown above.';
GO
