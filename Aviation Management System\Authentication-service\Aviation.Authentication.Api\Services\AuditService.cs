using Aviation.Authentication.Api.Data;
using Aviation.Authentication.Api.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;

namespace Aviation.Authentication.Api.Services;

public class AuditService : IAuditService
{
    private readonly AuthDbContext _context;
    private readonly ILogger<AuditService> _logger;

    public AuditService(AuthDbContext context, ILogger<AuditService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task LogAsync(string action, string resource, string details, int? clientId = null, 
                               string ipAddress = "", string userAgent = "", bool success = true, string? errorMessage = null)
    {
        try
        {
            var auditLog = new AuditLog
            {
                Action = action,
                Resource = resource,
                Details = details,
                ClientId = clientId,
                IpAddress = ipAddress,
                UserAgent = userAgent,
                Success = success,
                ErrorMessage = errorMessage,
                Timestamp = DateTime.UtcNow
            };

            _context.AuditLogs.Add(auditLog);
            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to write audit log for action {Action}", action);
            // Don't throw - audit logging should not break the main flow
        }
    }

    public async Task<IEnumerable<AuditLog>> GetAuditLogsAsync(int? clientId = null, DateTime? from = null, 
                                                               DateTime? to = null, int page = 1, int pageSize = 50)
    {
        var query = _context.AuditLogs.AsQueryable();

        if (clientId.HasValue)
            query = query.Where(a => a.ClientId == clientId.Value);

        if (from.HasValue)
            query = query.Where(a => a.Timestamp >= from.Value);

        if (to.HasValue)
            query = query.Where(a => a.Timestamp <= to.Value);

        return await query
            .OrderByDescending(a => a.Timestamp)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }
}

public class ScopeService : IScopeService
{
    private readonly AuthDbContext _context;
    private readonly ILogger<ScopeService> _logger;

    public ScopeService(AuthDbContext context, ILogger<ScopeService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Scope>> GetScopesAsync()
    {
        return await _context.Scopes
            .Where(s => s.IsActive)
            .OrderBy(s => s.Category)
            .ThenBy(s => s.Name)
            .ToListAsync();
    }

    public async Task<Scope?> GetScopeAsync(string name)
    {
        return await _context.Scopes
            .FirstOrDefaultAsync(s => s.Name == name);
    }

    public async Task<Scope> CreateScopeAsync(CreateScopeRequest request)
    {
        var scope = new Scope
        {
            Name = request.Name,
            DisplayName = request.DisplayName,
            Description = request.Description,
            Category = request.Category,
            IsRequired = request.IsRequired,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.Scopes.Add(scope);
        await _context.SaveChangesAsync();

        return scope;
    }

    public async Task<Scope?> UpdateScopeAsync(int id, UpdateScopeRequest request)
    {
        var scope = await _context.Scopes.FindAsync(id);
        if (scope == null)
            return null;

        if (!string.IsNullOrEmpty(request.DisplayName))
            scope.DisplayName = request.DisplayName;

        if (!string.IsNullOrEmpty(request.Description))
            scope.Description = request.Description;

        if (request.Category.HasValue)
            scope.Category = request.Category.Value;

        if (request.IsRequired.HasValue)
            scope.IsRequired = request.IsRequired.Value;

        if (request.IsActive.HasValue)
            scope.IsActive = request.IsActive.Value;

        await _context.SaveChangesAsync();
        return scope;
    }

    public async Task<bool> DeleteScopeAsync(int id)
    {
        var scope = await _context.Scopes.FindAsync(id);
        if (scope == null)
            return false;

        // Check if scope is in use by any clients
        var isInUse = await _context.ClientScopes.AnyAsync(cs => cs.ScopeId == id);
        if (isInUse)
        {
            // Soft delete - mark as inactive instead of removing
            scope.IsActive = false;
            await _context.SaveChangesAsync();
        }
        else
        {
            // Hard delete if not in use
            _context.Scopes.Remove(scope);
            await _context.SaveChangesAsync();
        }

        return true;
    }

    public async Task<bool> ValidateScopesAsync(IEnumerable<string> scopes)
    {
        var validScopes = await _context.Scopes
            .Where(s => s.IsActive && scopes.Contains(s.Name))
            .Select(s => s.Name)
            .ToListAsync();

        return scopes.All(s => validScopes.Contains(s));
    }
}

public class RateLimitService : IRateLimitService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<RateLimitService> _logger;

    public RateLimitService(IMemoryCache cache, ILogger<RateLimitService> logger)
    {
        _cache = cache;
        _logger = logger;
    }

    public async Task<bool> IsAllowedAsync(string clientId, string ipAddress)
    {
        var key = $"rate_limit_{clientId}_{ipAddress}";
        var rateLimitInfo = await GetRateLimitInfoAsync(clientId, ipAddress);
        
        return !rateLimitInfo.IsBlocked && rateLimitInfo.Remaining > 0;
    }

    public async Task IncrementUsageAsync(string clientId, string ipAddress)
    {
        var key = $"rate_limit_{clientId}_{ipAddress}";
        var currentHour = DateTime.UtcNow.ToString("yyyyMMddHH");
        var cacheKey = $"{key}_{currentHour}";

        var currentCount = _cache.Get<int>(cacheKey);
        _cache.Set(cacheKey, currentCount + 1, TimeSpan.FromHours(1));
    }

    public async Task<RateLimitInfo> GetRateLimitInfoAsync(string clientId, string ipAddress)
    {
        // This is a simplified in-memory implementation
        // In production, you might want to use Redis or a database for distributed rate limiting
        
        var key = $"rate_limit_{clientId}_{ipAddress}";
        var currentHour = DateTime.UtcNow.ToString("yyyyMMddHH");
        var cacheKey = $"{key}_{currentHour}";

        var currentCount = _cache.Get<int>(cacheKey);
        var limit = 1000; // Default rate limit per hour
        var remaining = Math.Max(0, limit - currentCount);
        var resetTime = DateTime.UtcNow.AddHours(1).Date.AddHours(DateTime.UtcNow.Hour + 1);

        return new RateLimitInfo
        {
            Limit = limit,
            Remaining = remaining,
            ResetTime = resetTime,
            IsBlocked = remaining <= 0
        };
    }
}
