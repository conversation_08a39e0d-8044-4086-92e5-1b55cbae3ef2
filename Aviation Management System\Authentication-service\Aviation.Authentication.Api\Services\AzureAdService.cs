using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Data;
using Microsoft.EntityFrameworkCore;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Text;
using Microsoft.Graph;
using Azure.Identity;
using Microsoft.Extensions.Options;

namespace Aviation.Authentication.Api.Services;

public class AzureAdService : IAzureAdService
{
    private readonly ILogger<AzureAdService> _logger;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly AzureAdConfiguration _azureAdOptions;

    public AzureAdService(ILogger<AzureAdService> logger, IOptions<AzureAdConfiguration> azureAdOptions)
    {
        _logger = logger;
        _azureAdOptions = azureAdOptions.Value;

        var options = new ClientSecretCredentialOptions
        {
            AuthorityHost = AzureAuthorityHosts.AzurePublicCloud,
        };

        var clientSecretCredential = new ClientSecretCredential(
            _azureAdOptions.TenantId,
            _azureAdOptions.ClientId,
            _azureAdOptions.ClientSecret,
            options);

        _graphServiceClient = new GraphServiceClient(clientSecretCredential);
    }

    public async Task<AzureAdUser?> ValidateTokenAsync(string accessToken)
    {
        try
        {
            _logger.LogInformation("Validating Azure AD token");

            // Use the user's access token to call Microsoft Graph API directly
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await httpClient.GetAsync("https://graph.microsoft.com/v1.0/me");

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to get user information from Microsoft Graph. Status: {Status}, Error: {Error}",
                    response.StatusCode, errorContent);
                return null;
            }

            var content = await response.Content.ReadAsStringAsync();
            var userInfo = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(content);

            if (userInfo == null)
            {
                _logger.LogWarning("Failed to parse user information from Microsoft Graph");
                return null;
            }

            return new AzureAdUser
            {
                ObjectId = Guid.Parse(userInfo["id"]?.ToString() ?? Guid.NewGuid().ToString()),
                DisplayName = userInfo.ContainsKey("displayName") ? userInfo["displayName"]?.ToString() ?? "" : "",
                Email = userInfo.ContainsKey("mail") ? userInfo["mail"]?.ToString() :
                        userInfo.ContainsKey("userPrincipalName") ? userInfo["userPrincipalName"]?.ToString() : "",
                UserPrincipalName = userInfo.ContainsKey("userPrincipalName") ? userInfo["userPrincipalName"]?.ToString() ?? "" : "",
                GivenName = userInfo.ContainsKey("givenName") ? userInfo["givenName"]?.ToString() ?? "" : "",
                Surname = userInfo.ContainsKey("surname") ? userInfo["surname"]?.ToString() ?? "" : "",
                JobTitle = userInfo.ContainsKey("jobTitle") ? userInfo["jobTitle"]?.ToString() ?? "" : "",
                Department = userInfo.ContainsKey("department") ? userInfo["department"]?.ToString() ?? "" : "",
                OfficeLocation = userInfo.ContainsKey("officeLocation") ? userInfo["officeLocation"]?.ToString() ?? "" : "",
                AccountEnabled = userInfo.ContainsKey("accountEnabled") ?
                    bool.TryParse(userInfo["accountEnabled"]?.ToString(), out var enabled) ? enabled : true : true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Azure AD token");
            return null;
        }
    }

    public async Task<AzureAdUser?> GetUserByObjectIdAsync(Guid objectId)
    {
        try
        {
            _logger.LogInformation("Getting Azure AD user by Object ID: {ObjectId}", objectId);

            var user = await _graphServiceClient.Users[objectId.ToString()].GetAsync();

            if (user == null)
            {
                _logger.LogWarning("User not found in Azure AD: {ObjectId}", objectId);
                return null;
            }

            return new AzureAdUser
            {
                ObjectId = Guid.Parse(user.Id!),
                DisplayName = user.DisplayName ?? "",
                Email = user.Mail ?? user.UserPrincipalName ?? "",
                UserPrincipalName = user.UserPrincipalName ?? "",
                GivenName = user.GivenName ?? "",
                Surname = user.Surname ?? "",
                JobTitle = user.JobTitle ?? "",
                Department = user.Department ?? "",
                OfficeLocation = user.OfficeLocation ?? ""
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Azure AD user by Object ID: {ObjectId}", objectId);
            return null;
        }
    }

    public async Task<AzureAdUser?> GetUserByEmailAsync(string email)
    {
        try
        {
            _logger.LogInformation("Getting Azure AD user by email: {Email}", email);

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = $"mail eq '{email}' or userPrincipalName eq '{email}'";
                    requestConfiguration.QueryParameters.Top = 1;
                });

            var user = users?.Value?.FirstOrDefault();
            if (user == null)
            {
                _logger.LogWarning("User not found in Azure AD: {Email}", email);
                return null;
            }

            return new AzureAdUser
            {
                ObjectId = Guid.Parse(user.Id!),
                DisplayName = user.DisplayName ?? "",
                Email = user.Mail ?? user.UserPrincipalName ?? "",
                UserPrincipalName = user.UserPrincipalName ?? "",
                GivenName = user.GivenName ?? "",
                Surname = user.Surname ?? "",
                JobTitle = user.JobTitle ?? "",
                Department = user.Department ?? "",
                OfficeLocation = user.OfficeLocation ?? ""
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Azure AD user by email: {Email}", email);
            return null;
        }
    }

    public async Task<IEnumerable<AzureAdUser>> GetUsersAsync(string? filter = null)
    {
        try
        {
            _logger.LogInformation("Getting Azure AD users with filter: {Filter}", filter ?? "none");

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    if (!string.IsNullOrEmpty(filter))
                    {
                        requestConfiguration.QueryParameters.Filter = filter;
                    }
                    requestConfiguration.QueryParameters.Top = 100; // Limit to 100 users
                });

            if (users?.Value == null)
            {
                return Enumerable.Empty<AzureAdUser>();
            }

            return users.Value.Select(user => new AzureAdUser
            {
                ObjectId = Guid.Parse(user.Id!),
                DisplayName = user.DisplayName ?? "",
                Email = user.Mail ?? user.UserPrincipalName ?? "",
                UserPrincipalName = user.UserPrincipalName ?? "",
                GivenName = user.GivenName ?? "",
                Surname = user.Surname ?? "",
                JobTitle = user.JobTitle ?? "",
                Department = user.Department ?? "",
                OfficeLocation = user.OfficeLocation ?? ""
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Azure AD users");
            return Enumerable.Empty<AzureAdUser>();
        }
    }

    public async Task<bool> IsUserInGroupAsync(Guid userId, string groupName)
    {
        try
        {
            _logger.LogInformation("Checking if user {UserId} is in group: {GroupName}", userId, groupName);

            var memberOf = await _graphServiceClient.Users[userId.ToString()].MemberOf
                .GetAsync();

            if (memberOf?.Value == null)
            {
                return false;
            }

            return memberOf.Value.Any(group =>
                group is Microsoft.Graph.Models.Group g &&
                (g.DisplayName?.Equals(groupName, StringComparison.OrdinalIgnoreCase) == true ||
                 g.MailNickname?.Equals(groupName, StringComparison.OrdinalIgnoreCase) == true));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if user {UserId} is in group: {GroupName}", userId, groupName);
            return false;
        }
    }

    public async Task<IEnumerable<string>> GetUserGroupsAsync(Guid userId)
    {
        try
        {
            _logger.LogInformation("Getting groups for user: {UserId}", userId);

            var memberOf = await _graphServiceClient.Users[userId.ToString()].MemberOf
                .GetAsync();

            if (memberOf?.Value == null)
            {
                return Enumerable.Empty<string>();
            }

            return memberOf.Value
                .OfType<Microsoft.Graph.Models.Group>()
                .Where(g => !string.IsNullOrEmpty(g.DisplayName))
                .Select(g => g.DisplayName!)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting groups for user: {UserId}", userId);
            return Enumerable.Empty<string>();
        }
    }

    public async Task<AzureAdAuthResult> AuthenticateUserAsync(string email, string password)
    {
        try
        {
            _logger.LogInformation("Authenticating user {Email} with Azure AD", email);
            _logger.LogInformation("Using TenantId: {TenantId}, ClientId: {ClientId}", _azureAdOptions.TenantId, _azureAdOptions.ClientId);

            // Use Resource Owner Password Credentials (ROPC) flow
            var httpClient = new HttpClient();
            var tokenEndpoint = $"https://login.microsoftonline.com/{_azureAdOptions.TenantId}/oauth2/v2.0/token";
            _logger.LogInformation("Token endpoint: {TokenEndpoint}", tokenEndpoint);

            var requestBody = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("client_id", _azureAdOptions.ClientId),
                new KeyValuePair<string, string>("client_secret", _azureAdOptions.ClientSecret),
                new KeyValuePair<string, string>("scope", "https://graph.microsoft.com/.default"),
                new KeyValuePair<string, string>("username", email),
                new KeyValuePair<string, string>("password", password),
                new KeyValuePair<string, string>("grant_type", "password")
            });

            _logger.LogInformation("Sending ROPC request for user {Email} with grant_type=password", email);

            var response = await httpClient.PostAsync(tokenEndpoint, requestBody);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("Azure AD response status: {StatusCode} for user {Email}", response.StatusCode, email);
            _logger.LogInformation("Azure AD response content: {Content}", responseContent);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Azure AD authentication failed for user {Email}. Status: {Status}, Response: {Response}",
                    email, response.StatusCode, responseContent);

                // Parse error response to detect MFA requirements
                var errorResult = ParseAzureAdError(responseContent, email);
                return errorResult;
            }

            // Parse token response
            var tokenResponse = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);
            var accessToken = tokenResponse?["access_token"]?.ToString();

            if (string.IsNullOrEmpty(accessToken))
            {
                return new AzureAdAuthResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Failed to obtain access token",
                    ErrorCode = "NO_ACCESS_TOKEN"
                };
            }

            // Get user info from Graph API
            var graphClient = new HttpClient();
            graphClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var userResponse = await graphClient.GetAsync("https://graph.microsoft.com/v1.0/me");
            if (!userResponse.IsSuccessStatusCode)
            {
                return new AzureAdAuthResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Failed to retrieve user information",
                    ErrorCode = "USER_INFO_FAILED"
                };
            }

            var userContent = await userResponse.Content.ReadAsStringAsync();
            var userInfo = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(userContent);

            if (userInfo == null)
            {
                return new AzureAdAuthResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Failed to parse user information",
                    ErrorCode = "USER_PARSE_FAILED"
                };
            }

            var azureUser = new AzureAdUser
            {
                ObjectId = Guid.Parse(userInfo["id"]?.ToString() ?? Guid.NewGuid().ToString()),
                Email = userInfo.ContainsKey("mail") ? userInfo["mail"]?.ToString() :
                        userInfo.ContainsKey("userPrincipalName") ? userInfo["userPrincipalName"]?.ToString() : email,
                GivenName = userInfo.ContainsKey("givenName") ? userInfo["givenName"]?.ToString() ?? "" : "",
                Surname = userInfo.ContainsKey("surname") ? userInfo["surname"]?.ToString() ?? "" : "",
                DisplayName = userInfo.ContainsKey("displayName") ? userInfo["displayName"]?.ToString() ?? "" : "",
                UserPrincipalName = userInfo.ContainsKey("userPrincipalName") ? userInfo["userPrincipalName"]?.ToString() ?? email : email
            };

            _logger.LogInformation("Successfully authenticated user {Email} with Azure AD", email);

            return new AzureAdAuthResult
            {
                IsSuccess = true,
                User = azureUser,
                AccessToken = accessToken
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error authenticating user {Email} with Azure AD", email);
            return new AzureAdAuthResult
            {
                IsSuccess = false,
                ErrorMessage = "Authentication service error",
                ErrorCode = "SERVICE_ERROR"
            };
        }
    }

    /// <summary>
    /// Parse Azure AD error response to detect specific error conditions like MFA requirements
    /// </summary>
    private AzureAdAuthResult ParseAzureAdError(string errorResponse, string email)
    {
        try
        {
            var errorData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(errorResponse);

            if (errorData != null)
            {
                var error = errorData.ContainsKey("error") ? errorData["error"]?.ToString() : "";
                var errorDescription = errorData.ContainsKey("error_description") ? errorData["error_description"]?.ToString() : "";
                var errorCodes = new List<string>();

                // Extract error codes if present
                if (errorData.ContainsKey("error_codes") && errorData["error_codes"] is System.Text.Json.JsonElement errorCodesElement)
                {
                    if (errorCodesElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        foreach (var code in errorCodesElement.EnumerateArray())
                        {
                            if (code.ValueKind == System.Text.Json.JsonValueKind.Number)
                            {
                                errorCodes.Add(code.GetInt32().ToString());
                            }
                        }
                    }
                }

                _logger.LogInformation("Azure AD Error Details - Error: {Error}, Description: {Description}, Codes: {Codes}",
                    error, errorDescription, string.Join(", ", errorCodes));

                // Check for MFA-related errors
                if (IsMfaRequiredError(error, errorDescription, errorCodes))
                {
                    _logger.LogWarning("MFA is required for user {Email}", email);
                    return new AzureAdAuthResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"Multi-Factor Authentication (MFA) is enabled for account '{email}'. Please use the OAuth2 flow for authentication with MFA support.",
                        ErrorCode = "MFA_REQUIRED"
                    };
                }

                // Check for conditional access policy errors
                if (IsConditionalAccessError(error, errorDescription, errorCodes))
                {
                    _logger.LogWarning("Conditional Access policy blocks authentication for user {Email}", email);
                    return new AzureAdAuthResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"Account '{email}' is subject to Conditional Access policies that require additional authentication. Please use the OAuth2 flow.",
                        ErrorCode = "CONDITIONAL_ACCESS_REQUIRED"
                    };
                }

                // Check for account disabled/locked errors
                if (IsAccountDisabledError(error, errorDescription, errorCodes))
                {
                    _logger.LogWarning("Account is disabled or locked for user {Email}", email);
                    return new AzureAdAuthResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"Account '{email}' is disabled or locked in Azure AD. Please contact your administrator.",
                        ErrorCode = "ACCOUNT_DISABLED"
                    };
                }

                // Check for password expired errors
                if (IsPasswordExpiredError(error, errorDescription, errorCodes))
                {
                    _logger.LogWarning("Password expired for user {Email}", email);
                    return new AzureAdAuthResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"Password for account '{email}' has expired. Please reset your password.",
                        ErrorCode = "PASSWORD_EXPIRED"
                    };
                }

                // Generic error with specific description if available
                if (!string.IsNullOrEmpty(errorDescription))
                {
                    return new AzureAdAuthResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"Azure AD authentication failed for '{email}': {errorDescription}",
                        ErrorCode = error?.ToUpper() ?? "AZURE_AUTH_FAILED"
                    };
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing Azure AD error response");
        }

        // Fallback generic error
        return new AzureAdAuthResult
        {
            IsSuccess = false,
            ErrorMessage = $"Invalid credentials or user '{email}' not found in Azure AD",
            ErrorCode = "AZURE_AUTH_FAILED"
        };
    }

    /// <summary>
    /// Check if the error indicates MFA is required
    /// </summary>
    private static bool IsMfaRequiredError(string? error, string? errorDescription, List<string> errorCodes)
    {
        // Common MFA-related error codes and descriptions
        var mfaErrorCodes = new[] { "50076", "50079", "50074", "50158" };
        var mfaKeywords = new[] { "mfa", "multi-factor", "multifactor", "strong authentication", "additional authentication" };

        // Check error codes
        if (errorCodes.Any(code => mfaErrorCodes.Contains(code)))
        {
            return true;
        }

        // Check error description for MFA keywords
        if (!string.IsNullOrEmpty(errorDescription))
        {
            var lowerDescription = errorDescription.ToLower();
            if (mfaKeywords.Any(keyword => lowerDescription.Contains(keyword)))
            {
                return true;
            }
        }

        // Check main error for MFA indicators
        if (!string.IsNullOrEmpty(error))
        {
            var lowerError = error.ToLower();
            if (mfaKeywords.Any(keyword => lowerError.Contains(keyword)))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Check if the error indicates Conditional Access policy requirements
    /// </summary>
    private static bool IsConditionalAccessError(string? error, string? errorDescription, List<string> errorCodes)
    {
        var caErrorCodes = new[] { "53003", "53000", "53001", "53002", "53004" };
        var caKeywords = new[] { "conditional access", "access policy", "compliance", "device" };

        return errorCodes.Any(code => caErrorCodes.Contains(code)) ||
               (!string.IsNullOrEmpty(errorDescription) && caKeywords.Any(keyword => errorDescription.ToLower().Contains(keyword)));
    }

    /// <summary>
    /// Check if the error indicates account is disabled or locked
    /// </summary>
    private static bool IsAccountDisabledError(string? error, string? errorDescription, List<string> errorCodes)
    {
        var disabledErrorCodes = new[] { "50057", "50058", "50034", "50053" };
        var disabledKeywords = new[] { "disabled", "locked", "blocked", "suspended" };

        return errorCodes.Any(code => disabledErrorCodes.Contains(code)) ||
               (!string.IsNullOrEmpty(errorDescription) && disabledKeywords.Any(keyword => errorDescription.ToLower().Contains(keyword)));
    }

    /// <summary>
    /// Check if the error indicates password has expired
    /// </summary>
    private static bool IsPasswordExpiredError(string? error, string? errorDescription, List<string> errorCodes)
    {
        var expiredErrorCodes = new[] { "50055", "50056", "50173" };
        var expiredKeywords = new[] { "password expired", "password has expired", "change password" };

        return errorCodes.Any(code => expiredErrorCodes.Contains(code)) ||
               (!string.IsNullOrEmpty(errorDescription) && expiredKeywords.Any(keyword => errorDescription.ToLower().Contains(keyword)));
    }
}

public class UserTokenService : IUserTokenService
{
    private readonly IConfiguration _configuration;
    private readonly AuthDbContext _context;
    private readonly ILogger<UserTokenService> _logger;

    public UserTokenService(IConfiguration configuration, AuthDbContext context, ILogger<UserTokenService> logger)
    {
        _configuration = configuration;
        _context = context;
        _logger = logger;
    }

    public async Task<string> GenerateUserTokenAsync(Aviation.Authentication.Api.Models.User user, List<string> permissions)
    {
        var key = Encoding.UTF8.GetBytes(_configuration["JWT:SecretKey"] ?? throw new InvalidOperationException("JWT secret key not configured"));
        var tokenHandler = new JwtSecurityTokenHandler();

        var tokenId = Guid.NewGuid().ToString();
        var issuedAt = DateTime.UtcNow;
        var expiresAt = issuedAt.AddHours(8); // 8-hour token for users

        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Jti, tokenId),
            new(JwtRegisteredClaimNames.Sub, user.UserId.ToString()),
            new(JwtRegisteredClaimNames.Email, user.Email),
            new("employee_id", user.EmployeeId),
            new("given_name", user.FirstName),
            new("family_name", user.LastName),
            new("azure_ad_object_id", user.AzureAdObjectId.ToString()),
            new("token_type", "user")
        };

        // Add role claims
        foreach (var userRole in user.UserRoles)
        {
            claims.Add(new Claim("role", userRole.Role.Name));
            claims.Add(new Claim("role_code", userRole.Role.RoleCode));
        }

        // Add permission claims
        foreach (var permission in permissions)
        {
            claims.Add(new Claim("permission", permission));
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = expiresAt,
            Issuer = _configuration["JWT:Issuer"],
            Audience = _configuration["JWT:Audience"],
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        // Store token in database for tracking
        // Use the test client (Id = 1) for user tokens since ClientId cannot be null
        var accessToken = new AccessToken
        {
            TokenId = tokenId,
            ClientId = 1, // Use test client for user tokens
            TokenHash = ComputeTokenHash(tokenString),
            IssuedAt = issuedAt,
            ExpiresAt = expiresAt,
            IpAddress = "",
            UserAgent = "",
            Scopes = string.Join(",", permissions)
        };

        _context.AccessTokens.Add(accessToken);
        await _context.SaveChangesAsync();

        return tokenString;
    }

    public async Task<UserTokenValidationResult> ValidateUserTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["JWT:SecretKey"] ?? throw new InvalidOperationException("JWT secret key not configured"));

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["JWT:Issuer"],
                ValidateAudience = true,
                ValidAudience = _configuration["JWT:Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);

            if (validatedToken is JwtSecurityToken jwtToken)
            {
                var jti = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;
                var tokenType = jwtToken.Claims.FirstOrDefault(x => x.Type == "token_type")?.Value;

                if (tokenType != "user")
                {
                    return new UserTokenValidationResult { IsValid = false, ErrorMessage = "Invalid token type" };
                }

                if (!string.IsNullOrEmpty(jti))
                {
                    // Check if token is revoked
                    var storedToken = await _context.AccessTokens
                        .FirstOrDefaultAsync(t => t.TokenId == jti && !t.IsRevoked);

                    if (storedToken == null || storedToken.ExpiresAt <= DateTime.UtcNow)
                    {
                        return new UserTokenValidationResult { IsValid = false, ErrorMessage = "Token not found or expired" };
                    }

                    var userIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Sub)?.Value;
                    var employeeIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == "employee_id")?.Value;
                    var permissions = jwtToken.Claims.Where(x => x.Type == "permission").Select(x => x.Value).ToList();
                    var roles = jwtToken.Claims.Where(x => x.Type == "role").Select(x => x.Value).ToList();

                    return new UserTokenValidationResult
                    {
                        IsValid = true,
                        UserId = Guid.TryParse(userIdClaim, out var userId) ? userId : null,
                        EmployeeId = employeeIdClaim,
                        Permissions = permissions,
                        Roles = roles,
                        ExpiresAt = storedToken.ExpiresAt
                    };
                }
            }

            return new UserTokenValidationResult { IsValid = false, ErrorMessage = "Invalid token format" };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "User token validation failed");
            return new UserTokenValidationResult { IsValid = false, ErrorMessage = "Token validation failed" };
        }
    }

    public async Task<bool> RevokeUserTokenAsync(string tokenId)
    {
        try
        {
            var token = await _context.AccessTokens
                .FirstOrDefaultAsync(t => t.TokenId == tokenId && !t.IsRevoked);

            if (token != null)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
                token.RevokedReason = "User token revoked";

                await _context.SaveChangesAsync();
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking user token {TokenId}", tokenId);
            return false;
        }
    }




    private static string ComputeTokenHash(string token)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(token));
        return Convert.ToBase64String(hashBytes);
    }
}
