using System.Security.Cryptography;
using System.Text;

namespace Aviation.Authentication.Api.Services;

/// <summary>
/// Service for encrypting and decrypting passwords using AES encryption
/// </summary>
public class CryptoService : ICryptoService
{
    private readonly byte[] _key;
    private readonly byte[] _iv;
    private readonly ILogger<CryptoService> _logger;

    public CryptoService(IConfiguration configuration, ILogger<CryptoService> logger)
    {
        _logger = logger;
        
        // Get encryption key from configuration
        var keyString = configuration["Crypto:EncryptionKey"];
        var ivString = configuration["Crypto:IV"];
        
        if (string.IsNullOrEmpty(keyString) || string.IsNullOrEmpty(ivString))
        {
            _logger.LogWarning("Crypto configuration not found, using default keys (NOT SECURE FOR PRODUCTION)");
            // Default keys for development (NOT SECURE FOR PRODUCTION)
            keyString = "aviation-auth-encryption-key-32-bytes-long!";
            ivString = "aviation-iv-16b!";
        }
        
        _key = Encoding.UTF8.GetBytes(keyString.PadRight(32).Substring(0, 32)); // Ensure 32 bytes for AES-256
        _iv = Encoding.UTF8.GetBytes(ivString.PadRight(16).Substring(0, 16));   // Ensure 16 bytes for IV
    }

    public string DecryptPassword(string encryptedPassword)
    {
        try
        {
            if (string.IsNullOrEmpty(encryptedPassword))
                throw new ArgumentException("Encrypted password cannot be null or empty", nameof(encryptedPassword));

            var encryptedBytes = Convert.FromBase64String(encryptedPassword);
            
            using var aes = Aes.Create();
            aes.Key = _key;
            aes.IV = _iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(encryptedBytes);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);
            
            var decryptedPassword = srDecrypt.ReadToEnd();
            _logger.LogDebug("Password decrypted successfully");
            
            return decryptedPassword;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt password");
            throw new InvalidOperationException("Failed to decrypt password", ex);
        }
    }

    public string EncryptPassword(string plainPassword)
    {
        try
        {
            if (string.IsNullOrEmpty(plainPassword))
                throw new ArgumentException("Plain password cannot be null or empty", nameof(plainPassword));

            using var aes = Aes.Create();
            aes.Key = _key;
            aes.IV = _iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);
            
            swEncrypt.Write(plainPassword);
            swEncrypt.Close();
            
            var encryptedBytes = msEncrypt.ToArray();
            var encryptedPassword = Convert.ToBase64String(encryptedBytes);
            
            _logger.LogDebug("Password encrypted successfully");
            
            return encryptedPassword;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt password");
            throw new InvalidOperationException("Failed to encrypt password", ex);
        }
    }

    public bool IsConfigured()
    {
        return _key != null && _key.Length == 32 && _iv != null && _iv.Length == 16;
    }
}
