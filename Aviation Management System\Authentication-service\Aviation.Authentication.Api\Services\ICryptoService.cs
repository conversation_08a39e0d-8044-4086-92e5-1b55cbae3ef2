namespace Aviation.Authentication.Api.Services;

/// <summary>
/// Service for encrypting and decrypting passwords for Azure AD authentication
/// </summary>
public interface ICryptoService
{
    /// <summary>
    /// Decrypt an encrypted password received from the client
    /// </summary>
    /// <param name="encryptedPassword">Base64 encoded encrypted password</param>
    /// <returns>Plain text password for Azure AD validation</returns>
    string DecryptPassword(string encryptedPassword);
    
    /// <summary>
    /// Encrypt a plain text password (for testing purposes)
    /// </summary>
    /// <param name="plainPassword">Plain text password</param>
    /// <returns>Base64 encoded encrypted password</returns>
    string EncryptPassword(string plainPassword);
    
    /// <summary>
    /// Validate that the encryption key is properly configured
    /// </summary>
    /// <returns>True if crypto service is ready, false otherwise</returns>
    bool IsConfigured();
}
