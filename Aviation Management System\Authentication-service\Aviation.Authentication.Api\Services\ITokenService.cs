using Aviation.Authentication.Api.Models;

namespace Aviation.Authentication.Api.Services;

public interface ITokenService
{
    Task<TokenResponse?> GenerateTokenAsync(TokenRequest request, string ipAddress, string userAgent);
    Task<bool> ValidateTokenAsync(string token);
    Task<bool> RevokeTokenAsync(string tokenId, string reason);
    Task CleanupExpiredTokensAsync();
    string? ValidateTokenAndGetClaims(string token);
}

public interface IClientService
{
    Task<Client?> GetClientAsync(string clientId);
    Task<Client?> GetClientByIdAsync(int id);
    Task<IEnumerable<Client>> GetClientsAsync(int page = 1, int pageSize = 20);
    Task<CreateClientResponse> CreateClientAsync(CreateClientRequest request, string createdBy);
    Task<ClientResponse?> UpdateClientAsync(int id, UpdateClientRequest request);
    Task<bool> DeleteClientAsync(int id);
    Task<bool> ValidateClientCredentialsAsync(string clientId, string clientSecret);
    Task<IEnumerable<string>> GetClientScopesAsync(string clientId);
    Task<bool> UpdateClientScopesAsync(int clientId, List<string> scopes, string updatedBy);
    Task<bool> UpdateClientStatusAsync(int clientId, ClientStatus status, string updatedBy);
    Task UpdateLastUsedAsync(string clientId);
}

public interface IScopeService
{
    Task<IEnumerable<Scope>> GetScopesAsync();
    Task<Scope?> GetScopeAsync(string name);
    Task<Scope> CreateScopeAsync(CreateScopeRequest request);
    Task<Scope?> UpdateScopeAsync(int id, UpdateScopeRequest request);
    Task<bool> DeleteScopeAsync(int id);
    Task<bool> ValidateScopesAsync(IEnumerable<string> scopes);
}

public interface IAuditService
{
    Task LogAsync(string action, string resource, string details, int? clientId = null, 
                  string ipAddress = "", string userAgent = "", bool success = true, string? errorMessage = null);
    Task<IEnumerable<AuditLog>> GetAuditLogsAsync(int? clientId = null, DateTime? from = null, 
                                                   DateTime? to = null, int page = 1, int pageSize = 50);
}

public interface IRateLimitService
{
    Task<bool> IsAllowedAsync(string clientId, string ipAddress);
    Task IncrementUsageAsync(string clientId, string ipAddress);
    Task<RateLimitInfo> GetRateLimitInfoAsync(string clientId, string ipAddress);
}

// Additional DTOs
public class UpdateClientRequest
{
    public string? ClientName { get; set; }
    public string? Description { get; set; }
    public ClientType? ClientType { get; set; }
    public int? AccessTokenLifetimeSeconds { get; set; }
    public int? RateLimitPerHour { get; set; }
    public string? AllowedIpAddresses { get; set; }
    public string? WebhookUrl { get; set; }
}

public class CreateScopeRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ScopeCategory Category { get; set; }
    public bool IsRequired { get; set; }
}

public class UpdateScopeRequest
{
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public ScopeCategory? Category { get; set; }
    public bool? IsRequired { get; set; }
    public bool? IsActive { get; set; }
}

public class RateLimitInfo
{
    public int Limit { get; set; }
    public int Remaining { get; set; }
    public DateTime ResetTime { get; set; }
    public bool IsBlocked { get; set; }
}
