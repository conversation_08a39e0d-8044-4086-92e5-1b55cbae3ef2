using Aviation.Authentication.Api.Data;
using Aviation.Authentication.Api.Models;
using Microsoft.EntityFrameworkCore;

namespace Aviation.Authentication.Api.Services;

public class ModuleService : IModuleService
{
    private readonly AuthDbContext _context;
    private readonly ILogger<ModuleService> _logger;

    public ModuleService(AuthDbContext context, ILogger<ModuleService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Module>> GetModulesAsync()
    {
        return await _context.Modules
            .Include(m => m.SubModules)
            .OrderBy(m => m.Name)
            .ToListAsync();
    }

    public async Task<Module?> GetModuleAsync(Guid moduleId)
    {
        return await _context.Modules
            .Include(m => m.SubModules)
            .FirstOrDefaultAsync(m => m.ModuleId == moduleId);
    }

    public async Task<Module> CreateModuleAsync(CreateModuleRequest request)
    {
        var module = new Module
        {
            Name = request.Name,
            Description = request.Description,
            CreatedDate = DateTime.UtcNow
        };

        _context.Modules.Add(module);
        await _context.SaveChangesAsync();

        return module;
    }

    public async Task<Module?> UpdateModuleAsync(Guid moduleId, UpdateModuleRequest request)
    {
        var module = await _context.Modules.FindAsync(moduleId);
        if (module == null)
            return null;

        if (!string.IsNullOrEmpty(request.Name))
            module.Name = request.Name;

        if (!string.IsNullOrEmpty(request.Description))
            module.Description = request.Description;

        await _context.SaveChangesAsync();
        return module;
    }

    public async Task<bool> DeleteModuleAsync(Guid moduleId)
    {
        var module = await _context.Modules
            .Include(m => m.SubModules)
            .FirstOrDefaultAsync(m => m.ModuleId == moduleId);
        
        if (module == null)
            return false;

        // Check if module is in use
        var isInUse = await _context.RolePermissions.AnyAsync(rp => rp.ModuleId == moduleId);
        if (isInUse)
        {
            throw new InvalidOperationException("Cannot delete module that is currently used in role permissions");
        }

        _context.Modules.Remove(module);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<IEnumerable<SubModule>> GetSubModulesAsync(Guid moduleId)
    {
        return await _context.SubModules
            .Where(sm => sm.ModuleId == moduleId)
            .OrderBy(sm => sm.Name)
            .ToListAsync();
    }

    public async Task<SubModule> CreateSubModuleAsync(Guid moduleId, CreateSubModuleRequest request)
    {
        var subModule = new SubModule
        {
            ModuleId = moduleId,
            Name = request.Name,
            Description = request.Description,
            CreatedDate = DateTime.UtcNow
        };

        _context.SubModules.Add(subModule);
        await _context.SaveChangesAsync();

        return subModule;
    }

    public async Task<SubModule?> UpdateSubModuleAsync(Guid subModuleId, UpdateSubModuleRequest request)
    {
        var subModule = await _context.SubModules.FindAsync(subModuleId);
        if (subModule == null)
            return null;

        if (!string.IsNullOrEmpty(request.Name))
            subModule.Name = request.Name;

        if (!string.IsNullOrEmpty(request.Description))
            subModule.Description = request.Description;

        await _context.SaveChangesAsync();
        return subModule;
    }

    public async Task<bool> DeleteSubModuleAsync(Guid subModuleId)
    {
        var subModule = await _context.SubModules.FindAsync(subModuleId);
        if (subModule == null)
            return false;

        // Check if submodule is in use
        var isInUse = await _context.RolePermissions.AnyAsync(rp => rp.SubModuleId == subModuleId);
        if (isInUse)
        {
            throw new InvalidOperationException("Cannot delete submodule that is currently used in role permissions");
        }

        _context.SubModules.Remove(subModule);
        await _context.SaveChangesAsync();

        return true;
    }
}

public class EntityService : IEntityService
{
    private readonly AuthDbContext _context;
    private readonly ILogger<EntityService> _logger;

    public EntityService(AuthDbContext context, ILogger<EntityService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Entity>> GetEntitiesAsync()
    {
        return await _context.Entities
            .OrderBy(e => e.Name)
            .ToListAsync();
    }

    public async Task<Entity?> GetEntityAsync(Guid entityId)
    {
        return await _context.Entities.FindAsync(entityId);
    }

    public async Task<Entity> CreateEntityAsync(CreateEntityRequest request)
    {
        var entity = new Entity
        {
            Name = request.Name,
            Description = request.Description,
            CreatedDate = DateTime.UtcNow
        };

        _context.Entities.Add(entity);
        await _context.SaveChangesAsync();

        return entity;
    }

    public async Task<Entity?> UpdateEntityAsync(Guid entityId, UpdateEntityRequest request)
    {
        var entity = await _context.Entities.FindAsync(entityId);
        if (entity == null)
            return null;

        if (!string.IsNullOrEmpty(request.Name))
            entity.Name = request.Name;

        if (!string.IsNullOrEmpty(request.Description))
            entity.Description = request.Description;

        await _context.SaveChangesAsync();
        return entity;
    }

    public async Task<bool> DeleteEntityAsync(Guid entityId)
    {
        var entity = await _context.Entities.FindAsync(entityId);
        if (entity == null)
            return false;

        // Check if entity is in use
        var isInUse = await _context.RolePermissions.AnyAsync(rp => rp.EntityId == entityId);
        if (isInUse)
        {
            throw new InvalidOperationException("Cannot delete entity that is currently used in role permissions");
        }

        _context.Entities.Remove(entity);
        await _context.SaveChangesAsync();

        return true;
    }
}
