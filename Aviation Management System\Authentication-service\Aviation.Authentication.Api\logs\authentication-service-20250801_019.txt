2025-08-01 12:41:48.770 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-01 12:41:48.857 +05:30 [INF] Now listening on: http://localhost:5293
2025-08-01 12:41:48.864 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-08-01 12:41:48.867 +05:30 [INF] Hosting environment: Development
2025-08-01 12:41:48.870 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-08-01 12:41:58.199 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91
2025-08-01 12:41:58.271 +05:30 [WRN] Failed to determine the https port for redirect.
2025-08-01 12:41:58.394 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 12:41:58.423 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 12:42:01.108 +05:30 [INF] Executed DbCommand (71ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 12:42:01.199 +05:30 [INF] Checking lockout for user EMP005. LockoutEnd: "2025-08-01T07:38:40.1613269", CurrentTime: "2025-08-01T07:12:01.1990008Z", IsLockedOut: true
2025-08-01 12:42:01.211 +05:30 [WRN] User EMP005 is locked out until "2025-08-01T07:38:40.1613269". Current time: "2025-08-01T07:12:01.2111186Z"
2025-08-01 12:42:01.365 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 12:42:01.396 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0, [LockoutEnd] = @p1
OUTPUT 1
WHERE [UserId] = @p2;
2025-08-01 12:42:01.416 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 12:42:01.435 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 2999.4421ms
2025-08-01 12:42:01.550 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 12:42:01.591 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 3394.2741ms
2025-08-01 12:43:43.665 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91
2025-08-01 12:43:43.688 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 12:43:43.696 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 12:43:43.742 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 12:43:43.757 +05:30 [INF] Checking lockout for user EMP005. LockoutEnd: null, CurrentTime: "2025-08-01T07:13:43.7572170Z", IsLockedOut: false
2025-08-01 12:43:43.763 +05:30 [INF] Authenticating user EMP005 with Azure AD using plain text password
2025-08-01 12:43:43.772 +05:30 [INF] <NAME_EMAIL> with Azure AD
2025-08-01 12:43:43.777 +05:30 [INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71
2025-08-01 12:43:43.783 +05:30 [INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token
2025-08-01 12:43:43.790 +05:30 [INF] Sending ROPC request <NAME_EMAIL> with grant_type=password
2025-08-01 12:43:45.294 +05:30 [INF] Azure AD response status: "BadRequest" <NAME_EMAIL>
2025-08-01 12:43:45.303 +05:30 [INF] Azure AD response content: {"error":"invalid_grant","error_description":"AADSTS50076: Due to a configuration change made by your administrator, or because you moved to a new location, you must use multi-factor authentication to access '00000003-0000-0000-c000-000000000000'. Trace ID: d60e1ebd-6270-48b5-a43a-0f92cc2e5601 Correlation ID: a557aa40-f671-4bf9-8261-b6d01cdc20d4 Timestamp: 2025-08-01 07:13:45Z","error_codes":[50076],"timestamp":"2025-08-01 07:13:45Z","trace_id":"d60e1ebd-6270-48b5-a43a-0f92cc2e5601","correlation_id":"a557aa40-f671-4bf9-8261-b6d01cdc20d4","error_uri":"https://login.microsoftonline.com/error?code=50076","suberror":"basic_action"}
2025-08-01 12:43:45.309 +05:30 [WRN] Azure AD authentication failed <NAME_EMAIL>. Status: "BadRequest", Response: {"error":"invalid_grant","error_description":"AADSTS50076: Due to a configuration change made by your administrator, or because you moved to a new location, you must use multi-factor authentication to access '00000003-0000-0000-c000-000000000000'. Trace ID: d60e1ebd-6270-48b5-a43a-0f92cc2e5601 Correlation ID: a557aa40-f671-4bf9-8261-b6d01cdc20d4 Timestamp: 2025-08-01 07:13:45Z","error_codes":[50076],"timestamp":"2025-08-01 07:13:45Z","trace_id":"d60e1ebd-6270-48b5-a43a-0f92cc2e5601","correlation_id":"a557aa40-f671-4bf9-8261-b6d01cdc20d4","error_uri":"https://login.microsoftonline.com/error?code=50076","suberror":"basic_action"}
2025-08-01 12:43:45.322 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 12:43:45.333 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0, [LockoutEnd] = @p1
OUTPUT 1
WHERE [UserId] = @p2;
2025-08-01 12:43:45.343 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 12:43:45.354 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 1648.15ms
2025-08-01 12:43:45.360 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 12:43:45.366 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 1701.3298ms
2025-08-01 12:50:02.182 +05:30 [INF] Application is shutting down...
[2025-08-01 12:50:07.825 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-08-01 12:50:07.943 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 12:50:07.954 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 12:50:07.959 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 12:50:07.966 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 12:50:12.468 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:12.653 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:12.707 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:12.789 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:18.232 +05:30 INF] Executed DbCommand (124ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:18.451 +05:30 INF] Checking lockout for user EMP005. LockoutEnd: "2025-08-01T07:43:45.3308422", CurrentTime: "2025-08-01T07:20:18.4513551Z", IsLockedOut: true {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:18.472 +05:30 WRN] User EMP005 is locked out until "2025-08-01T07:43:45.3308422". Current time: "2025-08-01T07:20:18.4719023Z" {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:18.904 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:19.008 +05:30 INF] Executed DbCommand (8ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0, [LockoutEnd] = @p1
OUTPUT 1
WHERE [UserId] = @p2; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:19.077 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:19.139 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 6313.8222ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:19.172 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:19.222 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 6760.0244ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:53.651 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:53.709 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:53.744 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:53.880 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:53.924 +05:30 INF] Checking lockout for user EMP005. LockoutEnd: null, CurrentTime: "2025-08-01T07:20:53.9241609Z", IsLockedOut: false {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:53.955 +05:30 INF] Authenticating user EMP005 with Azure AD using plain text password {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:53.991 +05:30 INF] <NAME_EMAIL> with Azure AD {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:54.013 +05:30 INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71 {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:54.040 +05:30 INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:54.070 +05:30 INF] Sending ROPC request <NAME_EMAIL> with grant_type=password {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:54.744 +05:30 INF] Azure AD response status: "OK" <NAME_EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:54.759 +05:30 INF] Azure AD response content: {"token_type":"Bearer","scope":"profile openid email https://graph.microsoft.com/Directory.Read.All https://graph.microsoft.com/Group.Read.All https://graph.microsoft.com/User.Read https://graph.microsoft.com/User.Read.All https://graph.microsoft.com/User.ReadWrite https://graph.microsoft.com/.default","expires_in":3714,"ext_expires_in":3714,"access_token":"eyJ0eXAiOiJKV1QiLCJub25jZSI6IjlaYjVNV1FORVdpQTVxR21qUHVQMUR5M3FUb3RxQnJhck4xYkM1anlsWGMiLCJhbGciOiJSUzI1NiIsIng1dCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LvSpUW8J4FWuzLpqsnKkwo0mKc17rnASPRiu2tHVLIUEHRpe5AwMjjtusrpVZApC2KneDDSAJI4S5YIDBKkWboTh7ldLekxQ89Fc1Z7U10ri9wEzfdfbouuVrCqhL0Qid2FVU6_Tdxr-FdGmzOKjqrwk65RiWRUlucjpUtQbSbnDe5WPIrkIA3WKvCN7T4Q8nSI2YyOKx5-rpY5gWS0SS32MQd3c7cyfzO8X83rq-MKww4Z10YtlrHUNTRi86TS515fymemWAYtvU_Esw_tdxsePidVlQck99dZ3dZ8DhXJe0C45U4iFr2RxW-KwNGqCvdjSmQDOHkdZAENQaStc1Q"} {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:55.569 +05:30 INF] Successfully <NAME_EMAIL> with Azure AD {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:55.604 +05:30 INF] User EMP005 authenticated successfully with Azure AD {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:55.761 +05:30 INF] Executed DbCommand (34ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:56.793 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:56.919 +05:30 INF] Executed DbCommand (18ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[UserId] = @__userId_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:56.956 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@p3='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0, [LastLogin] = @p1, [ModifiedDate] = @p2
OUTPUT 1
WHERE [UserId] = @p3; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:57.089 +05:30 INF] Executed DbCommand (14ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:57.156 +05:30 INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__userId_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:57.201 +05:30 INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:57.233 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"88b55676-902e-4fef-bd07-f31dbd33b2bb","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:57.327 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 3552.1492ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:57.344 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 12:50:57.358 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 200 null application/json; charset=utf-8 3706.4126ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPFDCA335:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPFDCA335"}
[2025-08-01 13:05:12.931 +05:30 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}
