2025-08-01 12:50:07.825 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-01 12:50:07.943 +05:30 [INF] Now listening on: http://localhost:5293
2025-08-01 12:50:07.954 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-08-01 12:50:07.959 +05:30 [INF] Hosting environment: Development
2025-08-01 12:50:07.966 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-08-01 12:50:12.468 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91
2025-08-01 12:50:12.653 +05:30 [WRN] Failed to determine the https port for redirect.
2025-08-01 12:50:12.707 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 12:50:12.789 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 12:50:18.232 +05:30 [INF] Executed DbCommand (124ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 12:50:18.451 +05:30 [INF] Checking lockout for user EMP005. LockoutEnd: "2025-08-01T07:43:45.3308422", CurrentTime: "2025-08-01T07:20:18.4513551Z", IsLockedOut: true
2025-08-01 12:50:18.472 +05:30 [WRN] User EMP005 is locked out until "2025-08-01T07:43:45.3308422". Current time: "2025-08-01T07:20:18.4719023Z"
2025-08-01 12:50:18.904 +05:30 [INF] Executed DbCommand (29ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 12:50:19.008 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0, [LockoutEnd] = @p1
OUTPUT 1
WHERE [UserId] = @p2;
2025-08-01 12:50:19.077 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 12:50:19.139 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 6313.8222ms
2025-08-01 12:50:19.172 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 12:50:19.222 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 6760.0244ms
2025-08-01 12:50:53.651 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91
2025-08-01 12:50:53.709 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 12:50:53.744 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 12:50:53.880 +05:30 [INF] Executed DbCommand (29ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 12:50:53.924 +05:30 [INF] Checking lockout for user EMP005. LockoutEnd: null, CurrentTime: "2025-08-01T07:20:53.9241609Z", IsLockedOut: false
2025-08-01 12:50:53.955 +05:30 [INF] Authenticating user EMP005 with Azure AD using plain text password
2025-08-01 12:50:53.991 +05:30 [INF] <NAME_EMAIL> with Azure AD
2025-08-01 12:50:54.013 +05:30 [INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71
2025-08-01 12:50:54.040 +05:30 [INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token
2025-08-01 12:50:54.070 +05:30 [INF] Sending ROPC request <NAME_EMAIL> with grant_type=password
2025-08-01 12:50:54.744 +05:30 [INF] Azure AD response status: "OK" <NAME_EMAIL>
2025-08-01 12:50:54.759 +05:30 [INF] Azure AD response content: {"token_type":"Bearer","scope":"profile openid email https://graph.microsoft.com/Directory.Read.All https://graph.microsoft.com/Group.Read.All https://graph.microsoft.com/User.Read https://graph.microsoft.com/User.Read.All https://graph.microsoft.com/User.ReadWrite https://graph.microsoft.com/.default","expires_in":3714,"ext_expires_in":3714,"access_token":"eyJ0eXAiOiJKV1QiLCJub25jZSI6IjlaYjVNV1FORVdpQTVxR21qUHVQMUR5M3FUb3RxQnJhck4xYkM1anlsWGMiLCJhbGciOiJSUzI1NiIsIng1dCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LvSpUW8J4FWuzLpqsnKkwo0mKc17rnASPRiu2tHVLIUEHRpe5AwMjjtusrpVZApC2KneDDSAJI4S5YIDBKkWboTh7ldLekxQ89Fc1Z7U10ri9wEzfdfbouuVrCqhL0Qid2FVU6_Tdxr-FdGmzOKjqrwk65RiWRUlucjpUtQbSbnDe5WPIrkIA3WKvCN7T4Q8nSI2YyOKx5-rpY5gWS0SS32MQd3c7cyfzO8X83rq-MKww4Z10YtlrHUNTRi86TS515fymemWAYtvU_Esw_tdxsePidVlQck99dZ3dZ8DhXJe0C45U4iFr2RxW-KwNGqCvdjSmQDOHkdZAENQaStc1Q"}
2025-08-01 12:50:55.569 +05:30 [INF] Successfully <NAME_EMAIL> with Azure AD
2025-08-01 12:50:55.604 +05:30 [INF] User EMP005 authenticated successfully with Azure AD
2025-08-01 12:50:55.761 +05:30 [INF] Executed DbCommand (34ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit)
2025-08-01 12:50:56.793 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-08-01 12:50:56.919 +05:30 [INF] Executed DbCommand (18ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[UserId] = @__userId_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 12:50:56.956 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@p3='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0, [LastLogin] = @p1, [ModifiedDate] = @p2
OUTPUT 1
WHERE [UserId] = @p3;
2025-08-01 12:50:57.089 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-01 12:50:57.156 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__userId_0
2025-08-01 12:50:57.201 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 12:50:57.233 +05:30 [INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'.
2025-08-01 12:50:57.327 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 3552.1492ms
2025-08-01 12:50:57.344 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 12:50:57.358 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 200 null application/json; charset=utf-8 3706.4126ms
2025-08-01 13:05:12.931 +05:30 [INF] Application is shutting down...
[2025-08-01 13:05:21.027 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-08-01 13:05:21.477 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 13:05:21.496 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 13:05:21.500 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 13:05:21.509 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 13:05:29.381 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:29.519 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:29.651 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:29.807 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:35.713 +05:30 INF] Executed DbCommand (273ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:36.015 +05:30 INF] Checking lockout for user EMP005. LockoutEnd: null, CurrentTime: "2025-08-01T07:35:36.0148746Z", IsLockedOut: false {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:36.047 +05:30 INF] Authenticating user EMP005 with Azure AD using plain text password {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:36.075 +05:30 INF] <NAME_EMAIL> with Azure AD {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:36.100 +05:30 INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71 {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:36.123 +05:30 INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:36.181 +05:30 INF] Sending ROPC request <NAME_EMAIL> with grant_type=password {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:36.854 +05:30 INF] Azure AD response status: "OK" <NAME_EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:36.883 +05:30 INF] Azure AD response content: {"token_type":"Bearer","scope":"profile openid email https://graph.microsoft.com/Directory.Read.All https://graph.microsoft.com/Group.Read.All https://graph.microsoft.com/User.Read https://graph.microsoft.com/User.Read.All https://graph.microsoft.com/User.ReadWrite https://graph.microsoft.com/.default","expires_in":4187,"ext_expires_in":4187,"access_token":"eyJ0eXAiOiJKV1QiLCJub25jZSI6Ik85TVVjN2JWOXF1X0hWd0o3M3JPTHA1azBGVVYtU1ViNTBoTjFnZjlsYWciLCJhbGciOiJSUzI1NiIsIng1dCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RcDx-BBSNK5YJIH9hnvzCaNXY31HXfgyM3UClWaK5Dzt8iciuTQCOV4pA7J_HNeqZWBQResrMkM2Fzx7259Ks9CU9QP2R339q83mtDqBgVe9bhofem_M25TyZWV2_KgwuvjIYuwcfTDEe26FURURknm8ycQO0v7jt3ffioGfr79aApGBe4LXsmxpJEUYWAQ0fjDkEauMKK2pvEoxxePKAEz7PClBq1-tRtKBeSLcvTlqqIlZN0_JJ8fWx6pFTZfARamzvVDRtpUCBZ834QzJwI7LCYUVA6mP8_QiSTlEbn1sDfJxwoDMoSwTWheYfryIsTrjW4FvB4MaQO7g5N1cJA"} {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:37.369 +05:30 INF] Successfully <NAME_EMAIL> with Azure AD {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:37.438 +05:30 INF] User EMP005 authenticated successfully with Azure AD {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:37.622 +05:30 INF] Executed DbCommand (33ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:38.369 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:38.643 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[UserId] = @__userId_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:38.758 +05:30 INF] Executed DbCommand (12ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [LastLogin] = @p0, [ModifiedDate] = @p1
OUTPUT 1
WHERE [UserId] = @p2; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:38.924 +05:30 INF] Executed DbCommand (14ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:38.994 +05:30 INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__userId_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:39.339 +05:30 INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:39.408 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:39.514 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 9668.7422ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:39.533 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:05:39.682 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 200 null application/json; charset=utf-8 10247.3767ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPNUKRE0D:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0D"}
[2025-08-01 13:08:08.583 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 92 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.621 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.637 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.775 +05:30 INF] Executed DbCommand (37ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.809 +05:30 INF] Checking lockout for user EMP006. LockoutEnd: null, CurrentTime: "2025-08-01T07:38:08.8088996Z", IsLockedOut: false {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.833 +05:30 INF] Authenticating user EMP006 with Azure AD using plain text password {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.860 +05:30 INF] <NAME_EMAIL> with Azure AD {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.876 +05:30 INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71 {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.895 +05:30 INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:08.912 +05:30 INF] Sending ROPC request <NAME_EMAIL> with grant_type=password {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.347 +05:30 INF] Azure AD response status: "BadRequest" <NAME_EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.375 +05:30 INF] Azure AD response content: {"error":"invalid_grant","error_description":"AADSTS50126: Error validating credentials due to invalid username or password. Trace ID: de0c76a3-ad13-4ea4-8c2d-2441e9f8ce00 Correlation ID: ec876760-8f15-4d68-af75-ec4eb41d0419 Timestamp: 2025-08-01 07:38:10Z","error_codes":[50126],"timestamp":"2025-08-01 07:38:10Z","trace_id":"de0c76a3-ad13-4ea4-8c2d-2441e9f8ce00","correlation_id":"ec876760-8f15-4d68-af75-ec4eb41d0419","error_uri":"https://login.microsoftonline.com/error?code=50126"} {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.400 +05:30 WRN] Azure AD authentication failed <NAME_EMAIL>. Status: "BadRequest", Response: {"error":"invalid_grant","error_description":"AADSTS50126: Error validating credentials due to invalid username or password. Trace ID: de0c76a3-ad13-4ea4-8c2d-2441e9f8ce00 Correlation ID: ec876760-8f15-4d68-af75-ec4eb41d0419 Timestamp: 2025-08-01 07:38:10Z","error_codes":[50126],"timestamp":"2025-08-01 07:38:10Z","trace_id":"de0c76a3-ad13-4ea4-8c2d-2441e9f8ce00","correlation_id":"ec876760-8f15-4d68-af75-ec4eb41d0419","error_uri":"https://login.microsoftonline.com/error?code=50126"} {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.442 +05:30 INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.494 +05:30 INF] Executed DbCommand (5ms) [Parameters=[@p1='?' (DbType = Guid), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0
OUTPUT 1
WHERE [UserId] = @p1; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.576 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3efbda9a-5be3-404a-ab09-705420c9169e","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.630 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 1966.4214ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.649 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 13:08:10.680 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 2095.3452ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPNUKRE0F:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGPNUKRE0F"}
[2025-08-01 14:26:09.325 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:09.895 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:10.031 +05:30 INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"7955b6d7-7a40-4001-b6b2-bec1d992b5bd","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:10.245 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"7955b6d7-7a40-4001-b6b2-bec1d992b5bd","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:10.288 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 232.4318ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:10.294 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:10.306 +05:30 INF] Session started; Key:e774f09e-7063-0b11-5d05-726e20e28a54, Id:c79ffdd4-87e4-0d09-b79e-f53c4af578cf {"EventId":{"Id":3,"Name":"SessionStarted"},"SourceContext":"Microsoft.AspNetCore.Session.DistributedSession","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:10.316 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 996.3604ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:45.296 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:45.327 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:45.331 +05:30 INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"7955b6d7-7a40-4001-b6b2-bec1d992b5bd","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:45.342 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"7955b6d7-7a40-4001-b6b2-bec1d992b5bd","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:45.350 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 10.1584ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:45.357 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:26:45.367 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 70.3623ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGPNUKRE0H:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGPNUKRE0H"}
[2025-08-01 14:32:21.969 +05:30 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}
