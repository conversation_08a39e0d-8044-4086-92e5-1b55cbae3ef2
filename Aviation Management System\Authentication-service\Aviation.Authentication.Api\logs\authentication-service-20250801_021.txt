2025-08-01 13:05:21.027 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-01 13:05:21.477 +05:30 [INF] Now listening on: http://localhost:5293
2025-08-01 13:05:21.496 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-08-01 13:05:21.500 +05:30 [INF] Hosting environment: Development
2025-08-01 13:05:21.509 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-08-01 13:05:29.381 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91
2025-08-01 13:05:29.519 +05:30 [WRN] Failed to determine the https port for redirect.
2025-08-01 13:05:29.651 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 13:05:29.807 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 13:05:35.713 +05:30 [INF] Executed DbCommand (273ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 13:05:36.015 +05:30 [INF] Checking lockout for user EMP005. LockoutEnd: null, CurrentTime: "2025-08-01T07:35:36.0148746Z", IsLockedOut: false
2025-08-01 13:05:36.047 +05:30 [INF] Authenticating user EMP005 with Azure AD using plain text password
2025-08-01 13:05:36.075 +05:30 [INF] <NAME_EMAIL> with Azure AD
2025-08-01 13:05:36.100 +05:30 [INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71
2025-08-01 13:05:36.123 +05:30 [INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token
2025-08-01 13:05:36.181 +05:30 [INF] Sending ROPC request <NAME_EMAIL> with grant_type=password
2025-08-01 13:05:36.854 +05:30 [INF] Azure AD response status: "OK" <NAME_EMAIL>
2025-08-01 13:05:36.883 +05:30 [INF] Azure AD response content: {"token_type":"Bearer","scope":"profile openid email https://graph.microsoft.com/Directory.Read.All https://graph.microsoft.com/Group.Read.All https://graph.microsoft.com/User.Read https://graph.microsoft.com/User.Read.All https://graph.microsoft.com/User.ReadWrite https://graph.microsoft.com/.default","expires_in":4187,"ext_expires_in":4187,"access_token":"eyJ0eXAiOiJKV1QiLCJub25jZSI6Ik85TVVjN2JWOXF1X0hWd0o3M3JPTHA1azBGVVYtU1ViNTBoTjFnZjlsYWciLCJhbGciOiJSUzI1NiIsIng1dCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RcDx-BBSNK5YJIH9hnvzCaNXY31HXfgyM3UClWaK5Dzt8iciuTQCOV4pA7J_HNeqZWBQResrMkM2Fzx7259Ks9CU9QP2R339q83mtDqBgVe9bhofem_M25TyZWV2_KgwuvjIYuwcfTDEe26FURURknm8ycQO0v7jt3ffioGfr79aApGBe4LXsmxpJEUYWAQ0fjDkEauMKK2pvEoxxePKAEz7PClBq1-tRtKBeSLcvTlqqIlZN0_JJ8fWx6pFTZfARamzvVDRtpUCBZ834QzJwI7LCYUVA6mP8_QiSTlEbn1sDfJxwoDMoSwTWheYfryIsTrjW4FvB4MaQO7g5N1cJA"}
2025-08-01 13:05:37.369 +05:30 [INF] Successfully <NAME_EMAIL> with Azure AD
2025-08-01 13:05:37.438 +05:30 [INF] User EMP005 authenticated successfully with Azure AD
2025-08-01 13:05:37.622 +05:30 [INF] Executed DbCommand (33ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit)
2025-08-01 13:05:38.369 +05:30 [INF] Executed DbCommand (32ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-08-01 13:05:38.643 +05:30 [INF] Executed DbCommand (30ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[UserId] = @__userId_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 13:05:38.758 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [LastLogin] = @p0, [ModifiedDate] = @p1
OUTPUT 1
WHERE [UserId] = @p2;
2025-08-01 13:05:38.924 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-01 13:05:38.994 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__userId_0
2025-08-01 13:05:39.339 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 13:05:39.408 +05:30 [INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'.
2025-08-01 13:05:39.514 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 9668.7422ms
2025-08-01 13:05:39.533 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 13:05:39.682 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 200 null application/json; charset=utf-8 10247.3767ms
2025-08-01 13:08:08.583 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 92
2025-08-01 13:08:08.621 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 13:08:08.637 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 13:08:08.775 +05:30 [INF] Executed DbCommand (37ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 13:08:08.809 +05:30 [INF] Checking lockout for user EMP006. LockoutEnd: null, CurrentTime: "2025-08-01T07:38:08.8088996Z", IsLockedOut: false
2025-08-01 13:08:08.833 +05:30 [INF] Authenticating user EMP006 with Azure AD using plain text password
2025-08-01 13:08:08.860 +05:30 [INF] <NAME_EMAIL> with Azure AD
2025-08-01 13:08:08.876 +05:30 [INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71
2025-08-01 13:08:08.895 +05:30 [INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token
2025-08-01 13:08:08.912 +05:30 [INF] Sending ROPC request <NAME_EMAIL> with grant_type=password
2025-08-01 13:08:10.347 +05:30 [INF] Azure AD response status: "BadRequest" <NAME_EMAIL>
2025-08-01 13:08:10.375 +05:30 [INF] Azure AD response content: {"error":"invalid_grant","error_description":"AADSTS50126: Error validating credentials due to invalid username or password. Trace ID: de0c76a3-ad13-4ea4-8c2d-2441e9f8ce00 Correlation ID: ec876760-8f15-4d68-af75-ec4eb41d0419 Timestamp: 2025-08-01 07:38:10Z","error_codes":[50126],"timestamp":"2025-08-01 07:38:10Z","trace_id":"de0c76a3-ad13-4ea4-8c2d-2441e9f8ce00","correlation_id":"ec876760-8f15-4d68-af75-ec4eb41d0419","error_uri":"https://login.microsoftonline.com/error?code=50126"}
2025-08-01 13:08:10.400 +05:30 [WRN] Azure AD authentication failed <NAME_EMAIL>. Status: "BadRequest", Response: {"error":"invalid_grant","error_description":"AADSTS50126: Error validating credentials due to invalid username or password. Trace ID: de0c76a3-ad13-4ea4-8c2d-2441e9f8ce00 Correlation ID: ec876760-8f15-4d68-af75-ec4eb41d0419 Timestamp: 2025-08-01 07:38:10Z","error_codes":[50126],"timestamp":"2025-08-01 07:38:10Z","trace_id":"de0c76a3-ad13-4ea4-8c2d-2441e9f8ce00","correlation_id":"ec876760-8f15-4d68-af75-ec4eb41d0419","error_uri":"https://login.microsoftonline.com/error?code=50126"}
2025-08-01 13:08:10.442 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 13:08:10.494 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@p1='?' (DbType = Guid), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0
OUTPUT 1
WHERE [UserId] = @p1;
2025-08-01 13:08:10.576 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 13:08:10.630 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 1966.4214ms
2025-08-01 13:08:10.649 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 13:08:10.680 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 2095.3452ms
2025-08-01 14:26:09.325 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 14:26:09.895 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:26:10.031 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:26:10.245 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:26:10.288 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 232.4318ms
2025-08-01 14:26:10.294 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:26:10.306 +05:30 [INF] Session started; Key:e774f09e-7063-0b11-5d05-726e20e28a54, Id:c79ffdd4-87e4-0d09-b79e-f53c4af578cf
2025-08-01 14:26:10.316 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 996.3604ms
2025-08-01 14:26:45.296 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 14:26:45.327 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:26:45.331 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:26:45.342 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:26:45.350 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 10.1584ms
2025-08-01 14:26:45.357 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:26:45.367 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 70.3623ms
2025-08-01 14:32:21.969 +05:30 [INF] Application is shutting down...
[2025-08-01 14:32:55.848 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-08-01 14:32:56.230 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 14:32:56.242 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 14:32:56.246 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 14:32:56.252 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 14:33:02.105 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:02.217 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:05.983 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:06.055 +05:30 INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:10.773 +05:30 INF] Accessing expired session, Key:e774f09e-7063-0b11-5d05-726e20e28a54 {"EventId":{"Id":2,"Name":"AccessingExpiredSession"},"SourceContext":"Microsoft.AspNetCore.Session.DistributedSession","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:10.815 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:10.908 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 4813.9151ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:10.939 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:10.966 +05:30 INF] Session started; Key:e774f09e-7063-0b11-5d05-726e20e28a54, Id:cbca68f9-c3a7-8b54-ed84-846960df962a {"EventId":{"Id":3,"Name":"SessionStarted"},"SourceContext":"Microsoft.AspNetCore.Session.DistributedSession","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:33:10.991 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 8888.1602ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305KQ:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KQ"}
[2025-08-01 14:34:28.272 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305KS:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KS"}
[2025-08-01 14:34:28.315 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305KS:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KS"}
[2025-08-01 14:34:28.334 +05:30 INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305KS:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KS"}
[2025-08-01 14:34:28.510 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305KS:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KS"}
[2025-08-01 14:34:28.927 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 560.0395ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGR8S305KS:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KS"}
[2025-08-01 14:34:28.952 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305KS:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KS"}
[2025-08-01 14:34:28.971 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 699.0038ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305KS:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KS"}
[2025-08-01 14:38:14.600 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305KU:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KU"}
[2025-08-01 14:38:15.039 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305KU:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KU"}
[2025-08-01 14:38:15.067 +05:30 INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305KU:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KU"}
[2025-08-01 14:38:15.198 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305KU:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KU"}
[2025-08-01 14:38:15.234 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 129.0431ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGR8S305KU:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KU"}
[2025-08-01 14:38:15.257 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305KU:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KU"}
[2025-08-01 14:38:15.284 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 685.0196ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305KU:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305KU"}
[2025-08-01 14:47:05.338 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1081 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305L0:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L0"}
[2025-08-01 14:47:05.396 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305L0:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L0"}
[2025-08-01 14:47:05.494 +05:30 INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"83218c01-ca20-4f0f-98ff-bd0d844d748d","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L0:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L0"}
[2025-08-01 14:47:05.715 +05:30 INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"83218c01-ca20-4f0f-98ff-bd0d844d748d","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L0:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L0"}
[2025-08-01 14:47:05.759 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 225.1015ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGR8S305L0:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L0"}
[2025-08-01 14:47:05.837 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305L0:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L0"}
[2025-08-01 14:47:05.864 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 400 null application/json; charset=utf-8 526.2692ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305L0:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L0"}
[2025-08-01 14:50:07.206 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305L2:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305L2"}
[2025-08-01 14:50:07.240 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305L2:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305L2"}
[2025-08-01 14:50:07.256 +05:30 INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L2:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305L2"}
[2025-08-01 14:50:07.280 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3f885fe5-2386-4d9f-b171-760863089c85","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L2:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305L2"}
[2025-08-01 14:50:07.303 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 25.4537ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGR8S305L2:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305L2"}
[2025-08-01 14:50:07.322 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305L2:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305L2"}
[2025-08-01 14:50:07.338 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 132.8997ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305L2:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGR8S305L2"}
[2025-08-01 14:52:53.576 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1114 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305L4:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L4"}
[2025-08-01 14:52:53.608 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305L4:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L4"}
[2025-08-01 14:52:53.623 +05:30 INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"83218c01-ca20-4f0f-98ff-bd0d844d748d","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L4:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L4"}
[2025-08-01 14:52:53.687 +05:30 INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"83218c01-ca20-4f0f-98ff-bd0d844d748d","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L4:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L4"}
[2025-08-01 14:52:53.777 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 106.6832ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGR8S305L4:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L4"}
[2025-08-01 14:52:53.846 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305L4:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L4"}
[2025-08-01 14:52:53.868 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 400 null application/json; charset=utf-8 291.9933ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305L4:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L4"}
[2025-08-01 14:54:01.614 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1057 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
[2025-08-01 14:54:01.659 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
[2025-08-01 14:54:01.694 +05:30 INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"83218c01-ca20-4f0f-98ff-bd0d844d748d","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
[2025-08-01 14:54:03.269 +05:30 INF] Validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"83218c01-ca20-4f0f-98ff-bd0d844d748d","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
[2025-08-01 14:54:08.321 +05:30 ERR] Error validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"83218c01-ca20-4f0f-98ff-bd0d844d748d","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
[2025-08-01 14:54:08.452 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"83218c01-ca20-4f0f-98ff-bd0d844d748d","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
[2025-08-01 14:54:08.487 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 6763.8809ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
[2025-08-01 14:54:08.505 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
[2025-08-01 14:54:08.543 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 401 null application/json; charset=utf-8 6929.2388ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGR8S305L6:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGR8S305L6"}
[2025-08-01 14:55:59.310 +05:30 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}
