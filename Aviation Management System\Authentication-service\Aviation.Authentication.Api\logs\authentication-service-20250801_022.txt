2025-08-01 14:32:55.848 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-01 14:32:56.230 +05:30 [INF] Now listening on: http://localhost:5293
2025-08-01 14:32:56.242 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-08-01 14:32:56.246 +05:30 [INF] Hosting environment: Development
2025-08-01 14:32:56.252 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-08-01 14:33:02.105 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 14:33:02.217 +05:30 [WRN] Failed to determine the https port for redirect.
2025-08-01 14:33:05.983 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:33:06.055 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:33:10.773 +05:30 [INF] Accessing expired session, Key:e774f09e-7063-0b11-5d05-726e20e28a54
2025-08-01 14:33:10.815 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:33:10.908 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 4813.9151ms
2025-08-01 14:33:10.939 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:33:10.966 +05:30 [INF] Session started; Key:e774f09e-7063-0b11-5d05-726e20e28a54, Id:cbca68f9-c3a7-8b54-ed84-846960df962a
2025-08-01 14:33:10.991 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 8888.1602ms
2025-08-01 14:34:28.272 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 14:34:28.315 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:34:28.334 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:34:28.510 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:34:28.927 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 560.0395ms
2025-08-01 14:34:28.952 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:34:28.971 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 699.0038ms
2025-08-01 14:38:14.600 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 14:38:15.039 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:38:15.067 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:38:15.198 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:38:15.234 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 129.0431ms
2025-08-01 14:38:15.257 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:38:15.284 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 685.0196ms
2025-08-01 14:47:05.338 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1081
2025-08-01 14:47:05.396 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 14:47:05.494 +05:30 [INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:47:05.715 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:47:05.759 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 225.1015ms
2025-08-01 14:47:05.837 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 14:47:05.864 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 400 null application/json; charset=utf-8 526.2692ms
2025-08-01 14:50:07.206 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 14:50:07.240 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:50:07.256 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:50:07.280 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:50:07.303 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 25.4537ms
2025-08-01 14:50:07.322 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:50:07.338 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 132.8997ms
2025-08-01 14:52:53.576 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1114
2025-08-01 14:52:53.608 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 14:52:53.623 +05:30 [INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:52:53.687 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:52:53.777 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 106.6832ms
2025-08-01 14:52:53.846 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 14:52:53.868 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 400 null application/json; charset=utf-8 291.9933ms
2025-08-01 14:54:01.614 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1057
2025-08-01 14:54:01.659 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 14:54:01.694 +05:30 [INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:54:03.269 +05:30 [INF] Validating Azure AD token
2025-08-01 14:54:08.321 +05:30 [ERR] Error validating Azure AD token
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
2025-08-01 14:54:08.452 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:54:08.487 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 6763.8809ms
2025-08-01 14:54:08.505 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 14:54:08.543 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 401 null application/json; charset=utf-8 6929.2388ms
2025-08-01 14:55:59.310 +05:30 [INF] Application is shutting down...
[2025-08-01 14:56:43.417 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-08-01 14:56:43.762 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 14:56:43.782 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 14:56:43.790 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 14:56:43.797 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 14:56:50.965 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1057 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:56:51.110 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:56:54.813 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:56:54.942 +05:30 INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:56:56.494 +05:30 INF] Accessing expired session, Key:e774f09e-7063-0b11-5d05-726e20e28a54 {"EventId":{"Id":2,"Name":"AccessingExpiredSession"},"SourceContext":"Microsoft.AspNetCore.Session.DistributedSession","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:56:56.536 +05:30 INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:56:56.586 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 1603.3867ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:56:56.615 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:56:56.704 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 400 null application/json; charset=utf-8 5734.6322ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.389 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.443 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.472 +05:30 INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"851f5265-ad82-43fa-8531-3b05008a82bc","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.526 +05:30 INF] Accessing expired session, Key:e774f09e-7063-0b11-5d05-726e20e28a54 {"EventId":{"Id":2,"Name":"AccessingExpiredSession"},"SourceContext":"Microsoft.AspNetCore.Session.DistributedSession","ActionId":"851f5265-ad82-43fa-8531-3b05008a82bc","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.552 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"851f5265-ad82-43fa-8531-3b05008a82bc","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.615 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 115.357ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.640 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.678 +05:30 INF] Session started; Key:e774f09e-7063-0b11-5d05-726e20e28a54, Id:694e51f9-2a3c-4a06-bcef-d47a19db3826 {"EventId":{"Id":3,"Name":"SessionStarted"},"SourceContext":"Microsoft.AspNetCore.Session.DistributedSession","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 14:57:03.763 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 373.4473ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRM5T7A6R:********","RequestPath":"/api/auth/azure-auth-url","ConnectionId":"0HNEGRM5T7A6R"}
[2025-08-01 15:00:12.304 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1054 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:12.377 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:12.390 +05:30 INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:13.289 +05:30 INF] Validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:21.286 +05:30 INF] Executed DbCommand (230ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:21.737 +05:30 INF] Executed DbCommand (45ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:22.813 +05:30 INF] Executed DbCommand (58ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:23.060 +05:30 INF] Executed DbCommand (23ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:23.104 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"5213f5f9-ca57-42a3-8da5-dbbe8cbe6d08","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:23.197 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 10791.9739ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:23.226 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:00:23.276 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 200 null application/json; charset=utf-8 10972.0555ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRM5T7A6T:********","RequestPath":"/api/auth/azure-callback","ConnectionId":"0HNEGRM5T7A6T"}
[2025-08-01 15:02:38.860 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 92 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:38.871 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:38.881 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"afa48782-479d-4bd5-b514-c4cef25112be","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:39.317 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"afa48782-479d-4bd5-b514-c4cef25112be","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:39.367 +05:30 INF] Checking lockout for user EMP006. LockoutEnd: null, CurrentTime: "2025-08-01T09:32:39.3673021Z", IsLockedOut: false {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"afa48782-479d-4bd5-b514-c4cef25112be","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:39.372 +05:30 INF] Authenticating user EMP006 with Azure AD using plain text password {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"afa48782-479d-4bd5-b514-c4cef25112be","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:39.390 +05:30 INF] <NAME_EMAIL> with Azure AD {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"afa48782-479d-4bd5-b514-c4cef25112be","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:39.399 +05:30 INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71 {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"afa48782-479d-4bd5-b514-c4cef25112be","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:39.404 +05:30 INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"afa48782-479d-4bd5-b514-c4cef25112be","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:02:39.410 +05:30 INF] Sending ROPC request <NAME_EMAIL> with grant_type=password {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"afa48782-479d-4bd5-b514-c4cef25112be","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRM5T7A6V:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRM5T7A6V"}
[2025-08-01 15:03:32.332 +05:30 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}
