2025-08-01 14:56:43.417 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-01 14:56:43.762 +05:30 [INF] Now listening on: http://localhost:5293
2025-08-01 14:56:43.782 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-08-01 14:56:43.790 +05:30 [INF] Hosting environment: Development
2025-08-01 14:56:43.797 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-08-01 14:56:50.965 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1057
2025-08-01 14:56:51.110 +05:30 [WRN] Failed to determine the https port for redirect.
2025-08-01 14:56:54.813 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 14:56:54.942 +05:30 [INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:56:56.494 +05:30 [INF] Accessing expired session, Key:e774f09e-7063-0b11-5d05-726e20e28a54
2025-08-01 14:56:56.536 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:56:56.586 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 1603.3867ms
2025-08-01 14:56:56.615 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 14:56:56.704 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 400 null application/json; charset=utf-8 5734.6322ms
2025-08-01 14:57:03.389 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 14:57:03.443 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:57:03.472 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 14:57:03.526 +05:30 [INF] Accessing expired session, Key:e774f09e-7063-0b11-5d05-726e20e28a54
2025-08-01 14:57:03.552 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 14:57:03.615 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 115.357ms
2025-08-01 14:57:03.640 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 14:57:03.678 +05:30 [INF] Session started; Key:e774f09e-7063-0b11-5d05-726e20e28a54, Id:694e51f9-2a3c-4a06-bcef-d47a19db3826
2025-08-01 14:57:03.763 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 373.4473ms
2025-08-01 15:00:12.304 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - application/json 1054
2025-08-01 15:00:12.377 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 15:00:12.390 +05:30 [INF] Route matched with {action = "HandleAzureCallback", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HandleAzureCallback(Aviation.Authentication.Api.Controllers.AzureCallbackRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 15:00:13.289 +05:30 [INF] Validating Azure AD token
2025-08-01 15:00:21.286 +05:30 [INF] Executed DbCommand (230ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 15:00:21.737 +05:30 [INF] Executed DbCommand (45ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit)
2025-08-01 15:00:22.813 +05:30 [INF] Executed DbCommand (58ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-08-01 15:00:23.060 +05:30 [INF] Executed DbCommand (23ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 15:00:23.104 +05:30 [INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'.
2025-08-01 15:00:23.197 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api) in 10791.9739ms
2025-08-01 15:00:23.226 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.HandleAzureCallback (Aviation.Authentication.Api)'
2025-08-01 15:00:23.276 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/azure-callback - 200 null application/json; charset=utf-8 10972.0555ms
2025-08-01 15:02:38.860 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 92
2025-08-01 15:02:38.871 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 15:02:38.881 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 15:02:39.317 +05:30 [INF] Executed DbCommand (28ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 15:02:39.367 +05:30 [INF] Checking lockout for user EMP006. LockoutEnd: null, CurrentTime: "2025-08-01T09:32:39.3673021Z", IsLockedOut: false
2025-08-01 15:02:39.372 +05:30 [INF] Authenticating user EMP006 with Azure AD using plain text password
2025-08-01 15:02:39.390 +05:30 [INF] <NAME_EMAIL> with Azure AD
2025-08-01 15:02:39.399 +05:30 [INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71
2025-08-01 15:02:39.404 +05:30 [INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token
2025-08-01 15:02:39.410 +05:30 [INF] Sending ROPC request <NAME_EMAIL> with grant_type=password
2025-08-01 15:03:32.332 +05:30 [INF] Application is shutting down...
[2025-08-01 15:04:18.471 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-08-01 15:04:18.585 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 15:04:18.593 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 15:04:18.595 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 15:04:18.598 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 15:05:06.257 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:06.305 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:06.423 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:06.449 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:07.845 +05:30 INF] Executed DbCommand (43ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:07.937 +05:30 INF] Checking lockout for user EMP005. LockoutEnd: null, CurrentTime: "2025-08-01T09:35:07.9371655Z", IsLockedOut: false {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:07.946 +05:30 INF] Authenticating user EMP005 with Azure AD using plain text password {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:07.955 +05:30 INF] <NAME_EMAIL> with Azure AD {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:07.962 +05:30 INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71 {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:07.969 +05:30 INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:05:07.976 +05:30 INF] Sending ROPC request <NAME_EMAIL> with grant_type=password {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:06:48.017 +05:30 ERR] Error <NAME_EMAIL> with Azure AD {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
System.Threading.Tasks.TaskCanceledException: The request was canceled due to the configured HttpClient.Timeout of 100 seconds elapsing.
 ---> System.TimeoutException: A task was canceled.
 ---> System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   --- End of inner exception stack trace ---
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpClient.HandleFailure(Exception e, Boolean telemetryStarted, HttpResponseMessage response, CancellationTokenSource cts, CancellationToken cancellationToken, CancellationTokenSource pendingRequestsCts)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.AuthenticateUserAsync(String email, String password) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 283
[2025-08-01 15:06:48.209 +05:30 INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:06:48.671 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@p1='?' (DbType = Guid), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0
OUTPUT 1
WHERE [UserId] = @p1; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:06:48.689 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"0201c55e-782a-422b-a85f-1c67a4fc8f28","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:06:48.704 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 102240.9141ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:06:48.711 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:06:48.725 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 102470.0575ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGRQPHATQO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGRQPHATQO"}
[2025-08-01 15:07:11.340 +05:30 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}
