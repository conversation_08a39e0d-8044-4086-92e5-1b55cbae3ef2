2025-08-01 15:14:39.414 +05:30 [WRN] 🚨 USING MOCK AZURE AD SERVICE - FOR TESTING ONLY! 🚨
2025-08-01 15:14:40.224 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-01 15:14:40.286 +05:30 [INF] Now listening on: http://localhost:5293
2025-08-01 15:14:40.291 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-08-01 15:14:40.293 +05:30 [INF] Hosting environment: Development
2025-08-01 15:14:40.295 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-08-01 15:15:20.839 +05:30 [INF] Application is shutting down...
[2025-08-01 15:15:28.768 +05:30 WRN] 🚨 USING MOCK AZURE AD SERVICE - FOR TESTING ONLY! 🚨 {}
[2025-08-01 15:15:29.103 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-08-01 15:15:29.193 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 15:15:29.205 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 15:15:29.208 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 15:15:29.211 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-01 15:15:37.982 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:38.166 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:39.856 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:39.892 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:40.523 +05:30 WRN] 🚨 USING MOCK AZURE AD SERVICE - FOR TESTING ONLY! 🚨 {"SourceContext":"Aviation.Authentication.Api.Services.MockAzureAdService","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:42.856 +05:30 INF] Executed DbCommand (173ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.029 +05:30 INF] Checking lockout for user EMP005. LockoutEnd: null, CurrentTime: "2025-08-01T09:45:43.0288113Z", IsLockedOut: false {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.042 +05:30 INF] Authenticating user EMP005 with Azure AD using plain text password {"SourceContext":"Aviation.Authentication.Api.Controllers.AuthController","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.251 +05:30 INF] 🔐 Mock: <NAME_EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.MockAzureAdService","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.259 +05:30 WRN] ❌ Mock: Authentication <NAME_EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.MockAzureAdService","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.441 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.479 +05:30 INF] Executed DbCommand (7ms) [Parameters=[@p1='?' (DbType = Guid), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0
OUTPUT 1
WHERE [UserId] = @p1; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.499 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ab33471-467a-4017-af0b-e848c1e9cfca","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.516 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 3608.4008ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.526 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:15:43.546 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 5565.2764ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEGS0LPS5JB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEGS0LPS5JB"}
[2025-08-01 15:16:01.702 +05:30 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}
