2025-08-01 16:56:44.938 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-01 16:56:45.064 +05:30 [INF] Now listening on: http://localhost:5293
2025-08-01 16:56:45.075 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-08-01 16:56:45.078 +05:30 [INF] Hosting environment: Development
2025-08-01 16:56:45.080 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-08-01 16:56:52.657 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 90
2025-08-01 16:56:52.809 +05:30 [WRN] Failed to determine the https port for redirect.
2025-08-01 16:56:56.948 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 16:56:57.110 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 16:57:03.410 +05:30 [INF] Executed DbCommand (332ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 16:57:03.555 +05:30 [INF] Checking lockout for user EMP005. LockoutEnd: "2025-08-01T10:30:13.4638133", CurrentTime: "2025-08-01T11:27:03.5555383Z", IsLockedOut: false
2025-08-01 16:57:03.566 +05:30 [INF] Authenticating user EMP005 with Azure AD using plain text password
2025-08-01 16:57:03.580 +05:30 [INF] <NAME_EMAIL> with Azure AD
2025-08-01 16:57:03.588 +05:30 [INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71
2025-08-01 16:57:03.596 +05:30 [INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token
2025-08-01 16:57:03.601 +05:30 [INF] Sending ROPC request <NAME_EMAIL> with grant_type=password
2025-08-01 16:57:05.138 +05:30 [INF] Azure AD response status: "BadRequest" <NAME_EMAIL>
2025-08-01 16:57:05.145 +05:30 [INF] Azure AD response content: {"error":"invalid_grant","error_description":"AADSTS50126: Error validating credentials due to invalid username or password. Trace ID: 51a9cad3-1178-4c93-bfd3-abe99516e600 Correlation ID: bf0e58a1-c302-46dc-9b40-b167faf57000 Timestamp: 2025-08-01 11:27:05Z","error_codes":[50126],"timestamp":"2025-08-01 11:27:05Z","trace_id":"51a9cad3-1178-4c93-bfd3-abe99516e600","correlation_id":"bf0e58a1-c302-46dc-9b40-b167faf57000","error_uri":"https://login.microsoftonline.com/error?code=50126"}
2025-08-01 16:57:05.151 +05:30 [WRN] Azure AD authentication failed <NAME_EMAIL>. Status: "BadRequest", Response: {"error":"invalid_grant","error_description":"AADSTS50126: Error validating credentials due to invalid username or password. Trace ID: 51a9cad3-1178-4c93-bfd3-abe99516e600 Correlation ID: bf0e58a1-c302-46dc-9b40-b167faf57000 Timestamp: 2025-08-01 11:27:05Z","error_codes":[50126],"timestamp":"2025-08-01 11:27:05Z","trace_id":"51a9cad3-1178-4c93-bfd3-abe99516e600","correlation_id":"bf0e58a1-c302-46dc-9b40-b167faf57000","error_uri":"https://login.microsoftonline.com/error?code=50126"}
2025-08-01 16:57:05.177 +05:30 [INF] Azure AD Error Details - Error: invalid_grant, Description: AADSTS50126: Error validating credentials due to invalid username or password. Trace ID: 51a9cad3-1178-4c93-bfd3-abe99516e600 Correlation ID: bf0e58a1-c302-46dc-9b40-b167faf57000 Timestamp: 2025-08-01 11:27:05Z, Codes: 50126
2025-08-01 16:57:05.384 +05:30 [INF] Executed DbCommand (41ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 16:57:05.456 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@p1='?' (DbType = Guid), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0
OUTPUT 1
WHERE [UserId] = @p1;
2025-08-01 16:57:06.030 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType6`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 16:57:06.068 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 8797.2924ms
2025-08-01 16:57:06.084 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 16:57:06.111 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 13455.0577ms
2025-08-01 16:59:00.653 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 91
2025-08-01 16:59:00.955 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 16:59:01.067 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 16:59:01.947 +05:30 [INF] Executed DbCommand (34ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 16:59:02.019 +05:30 [INF] Checking lockout for user EMP005. LockoutEnd: "2025-08-01T10:30:13.4638133", CurrentTime: "2025-08-01T11:29:02.0188209Z", IsLockedOut: false
2025-08-01 16:59:02.039 +05:30 [INF] Authenticating user EMP005 with Azure AD using plain text password
2025-08-01 16:59:02.072 +05:30 [INF] <NAME_EMAIL> with Azure AD
2025-08-01 16:59:02.103 +05:30 [INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71
2025-08-01 16:59:02.123 +05:30 [INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token
2025-08-01 16:59:02.140 +05:30 [INF] Sending ROPC request <NAME_EMAIL> with grant_type=password
2025-08-01 16:59:02.536 +05:30 [INF] Azure AD response status: "OK" <NAME_EMAIL>
2025-08-01 16:59:02.568 +05:30 [INF] Azure AD response content: {"token_type":"Bearer","scope":"profile openid email https://graph.microsoft.com/Directory.Read.All https://graph.microsoft.com/Group.Read.All https://graph.microsoft.com/User.Read https://graph.microsoft.com/User.Read.All https://graph.microsoft.com/User.ReadWrite https://graph.microsoft.com/.default","expires_in":5336,"ext_expires_in":5336,"access_token":"eyJ0eXAiOiJKV1QiLCJub25jZSI6ImtjcTU0R1N4cVJlQXhEb1BIcURCNk1OWXFkYXI3dHJQbHl3a19hTVRDTGciLCJhbGciOiJSUzI1NiIsIng1dCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jNFTQ2ckU1fP5KErHaSxewCT3T6KceMNbqyA36e9CSw1SnOzSrCLTCEZRhPIptmQGS5NVVuZTM0nHfMiZaeu-3GAUV19skhbMh6NzkDpIGhzYnWu1eZAOqYnp0CCTPVhgMJLFQiW-11n1LfSyJMY_C-elqlRRWy2Uacv37HVcr4APfdChOoR5xCFTYUXAGih78bGKsRt4kpbDig4lR4FIftjuKGdZb1WngYrMKj5vYmP5L4GtrJnpDnoeQSy3Wot-erzV25lqmkK3aO3TfrOKXW53q246-NoLIbDyHDSkIk5ApsSNUS_rSFepssa1Go5f5E6pAd_m_KSfsxvJz5h0A"}
2025-08-01 16:59:02.973 +05:30 [INF] Successfully <NAME_EMAIL> with Azure AD
2025-08-01 16:59:02.987 +05:30 [INF] User EMP005 authenticated successfully with Azure AD
2025-08-01 16:59:03.769 +05:30 [INF] Executed DbCommand (551ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit)
2025-08-01 16:59:04.935 +05:30 [INF] Executed DbCommand (50ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-08-01 16:59:05.134 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[UserId] = @__userId_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 16:59:05.184 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@p3='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0, [LastLogin] = @p1, [ModifiedDate] = @p2
OUTPUT 1
WHERE [UserId] = @p3;
2025-08-01 16:59:05.315 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-01 16:59:05.396 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__userId_0
2025-08-01 16:59:05.444 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 16:59:05.514 +05:30 [INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'.
2025-08-01 16:59:05.611 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 4323.4306ms
2025-08-01 16:59:05.649 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 16:59:05.675 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 200 null application/json; charset=utf-8 5033.5178ms
2025-08-01 16:59:28.508 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 93
2025-08-01 16:59:28.689 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 16:59:28.700 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 16:59:29.002 +05:30 [INF] Executed DbCommand (28ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId]
2025-08-01 16:59:29.031 +05:30 [INF] Checking lockout for user EMP05. LockoutEnd: null, CurrentTime: "2025-08-01T11:29:29.0317863Z", IsLockedOut: false
2025-08-01 16:59:29.051 +05:30 [INF] Authenticating user EMP05 with Azure AD using plain text password
2025-08-01 16:59:29.066 +05:30 [INF] <NAME_EMAIL> with Azure AD
2025-08-01 16:59:29.085 +05:30 [INF] Using TenantId: 14158288-a340-4380-88ed-a8989a932425, ClientId: 65567050-fbad-4326-b653-aa1f30edaa71
2025-08-01 16:59:29.106 +05:30 [INF] Token endpoint: https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/token
2025-08-01 16:59:29.124 +05:30 [INF] Sending ROPC request <NAME_EMAIL> with grant_type=password
2025-08-01 16:59:29.490 +05:30 [INF] Azure AD response status: "BadRequest" <NAME_EMAIL>
2025-08-01 16:59:29.513 +05:30 [INF] Azure AD response content: {"error":"invalid_grant","error_description":"AADSTS50076: Due to a configuration change made by your administrator, or because you moved to a new location, you must use multi-factor authentication to access '00000003-0000-0000-c000-000000000000'. Trace ID: c6db4cf0-2f0c-43bc-9003-d0b053cf1f01 Correlation ID: c030b2a5-f98a-45f2-9ffb-e50d3ba3550b Timestamp: 2025-08-01 11:29:29Z","error_codes":[50076],"timestamp":"2025-08-01 11:29:29Z","trace_id":"c6db4cf0-2f0c-43bc-9003-d0b053cf1f01","correlation_id":"c030b2a5-f98a-45f2-9ffb-e50d3ba3550b","error_uri":"https://login.microsoftonline.com/error?code=50076","suberror":"basic_action"}
2025-08-01 16:59:29.540 +05:30 [WRN] Azure AD authentication failed <NAME_EMAIL>. Status: "BadRequest", Response: {"error":"invalid_grant","error_description":"AADSTS50076: Due to a configuration change made by your administrator, or because you moved to a new location, you must use multi-factor authentication to access '00000003-0000-0000-c000-000000000000'. Trace ID: c6db4cf0-2f0c-43bc-9003-d0b053cf1f01 Correlation ID: c030b2a5-f98a-45f2-9ffb-e50d3ba3550b Timestamp: 2025-08-01 11:29:29Z","error_codes":[50076],"timestamp":"2025-08-01 11:29:29Z","trace_id":"c6db4cf0-2f0c-43bc-9003-d0b053cf1f01","correlation_id":"c030b2a5-f98a-45f2-9ffb-e50d3ba3550b","error_uri":"https://login.microsoftonline.com/error?code=50076","suberror":"basic_action"}
2025-08-01 16:59:29.569 +05:30 [INF] Azure AD Error Details - Error: invalid_grant, Description: AADSTS50076: Due to a configuration change made by your administrator, or because you moved to a new location, you must use multi-factor authentication to access '00000003-0000-0000-c000-000000000000'. Trace ID: c6db4cf0-2f0c-43bc-9003-d0b053cf1f01 Correlation ID: c030b2a5-f98a-45f2-9ffb-e50d3ba3550b Timestamp: 2025-08-01 11:29:29Z, Codes: 50076
2025-08-01 16:59:29.601 +05:30 [WRN] MFA is required <NAME_EMAIL>
2025-08-01 16:59:29.632 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-01 16:59:29.671 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FailedAttempts] = @p0, [LockoutEnd] = @p1
OUTPUT 1
WHERE [UserId] = @p2;
2025-08-01 16:59:29.704 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType6`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 16:59:29.724 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 1009.5815ms
2025-08-01 16:59:29.736 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-08-01 16:59:29.756 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 1247.8996ms
2025-08-01 16:59:51.856 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 16:59:51.940 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 16:59:52.098 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 16:59:52.190 +05:30 [INF] Accessing expired session, Key:e774f09e-7063-0b11-5d05-726e20e28a54
2025-08-01 16:59:52.240 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType4`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 16:59:52.272 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 145.1356ms
2025-08-01 16:59:52.294 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 16:59:52.316 +05:30 [INF] Session started; Key:e774f09e-7063-0b11-5d05-726e20e28a54, Id:cb07fd32-719c-9fbc-38c3-f758045e966d
2025-08-01 16:59:52.375 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 518.6792ms
2025-08-01 17:02:32.474 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - null null
2025-08-01 17:02:32.541 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 17:02:32.584 +05:30 [INF] Route matched with {action = "GetAzureAuthUrl", controller = "Auth"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAzureAuthUrl(System.String, Boolean) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-08-01 17:02:32.646 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType4`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-01 17:02:32.683 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api) in 58.1422ms
2025-08-01 17:02:32.720 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.GetAzureAuthUrl (Aviation.Authentication.Api)'
2025-08-01 17:02:32.752 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/auth/azure-auth-url - 200 null application/json; charset=utf-8 278.4521ms
