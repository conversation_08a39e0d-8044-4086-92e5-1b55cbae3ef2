-- =============================================
-- Aviation Authentication Service Database Setup
-- =============================================

-- Create Database
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AviationAuthentication')
BEGIN
    CREATE DATABASE AviationAuthentication;
    PRINT 'Database AviationAuthentication created successfully.';
END
ELSE
BEGIN
    PRINT 'Database AviationAuthentication already exists.';
END

-- Use the database
USE AviationAuthentication;

-- Enable snapshot isolation for better concurrency
ALTER DATABASE AviationAuthentication SET ALLOW_SNAPSHOT_ISOLATION ON;
ALTER DATABASE AviationAuthentication SET READ_COMMITTED_SNAPSHOT ON;

PRINT 'Database setup completed successfully.';
