-- =============================================
-- Create Authentication Service Tables
-- =============================================

USE AviationAuthentication;

-- =============================================
-- B2B OAuth 2.0 Tables
-- =============================================

-- Clients table for B2B applications
CREATE TABLE Clients (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    ClientId NVARCHAR(100) NOT NULL UNIQUE,
    ClientSecret NVARCHAR(500) NOT NULL,
    ClientName NVARCHAR(200) NOT NULL,
    Description NVARCHAR(1000) NULL,
    ClientType INT NOT NULL DEFAULT 0, -- 0=Airline, 1=TravelAgency, 2=CorporateClient, etc.
    Status INT NOT NULL DEFAULT 0, -- 0=Active, 1=Inactive, 2=Suspended, 3=PendingApproval, 4=Revoked
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastUsedAt DATETIME2 NULL,
    CreatedBy NVARCHAR(100) NOT NULL,
    AccessTokenLifetimeSeconds INT NOT NULL DEFAULT 3600,
    RateLimitPerHour INT NOT NULL DEFAULT 1000,
    AllowedIpAddresses NVARCHAR(1000) NULL,
    WebhookUrl NVARCHAR(500) NULL,
    WebhookSecret NVARCHAR(200) NULL
);

-- Scopes table for API permissions
CREATE TABLE Scopes (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL UNIQUE,
    DisplayName NVARCHAR(200) NULL,
    Description NVARCHAR(500) NULL,
    Category INT NOT NULL DEFAULT 0, -- 0=Partner, 1=Customer, 2=Order, etc.
    IsRequired BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

-- Client-Scope mapping table
CREATE TABLE ClientScopes (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    ClientId INT NOT NULL,
    ScopeId INT NOT NULL,
    GrantedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    GrantedBy NVARCHAR(100) NOT NULL,
    CONSTRAINT FK_ClientScopes_Clients FOREIGN KEY (ClientId) REFERENCES Clients(Id) ON DELETE CASCADE,
    CONSTRAINT FK_ClientScopes_Scopes FOREIGN KEY (ScopeId) REFERENCES Scopes(Id) ON DELETE CASCADE,
    CONSTRAINT UQ_ClientScopes_ClientId_ScopeId UNIQUE (ClientId, ScopeId)
);

-- Access tokens table
CREATE TABLE AccessTokens (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    TokenId NVARCHAR(100) NOT NULL UNIQUE,
    ClientId INT NOT NULL,
    TokenHash NVARCHAR(500) NOT NULL,
    IssuedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ExpiresAt DATETIME2 NOT NULL,
    IsRevoked BIT NOT NULL DEFAULT 0,
    RevokedAt DATETIME2 NULL,
    RevokedReason NVARCHAR(500) NULL,
    IpAddress NVARCHAR(50) NULL,
    UserAgent NVARCHAR(500) NULL,
    Scopes NVARCHAR(1000) NULL,
    CONSTRAINT FK_AccessTokens_Clients FOREIGN KEY (ClientId) REFERENCES Clients(Id) ON DELETE CASCADE
);

-- =============================================
-- User Management Tables
-- =============================================

-- Users table
CREATE TABLE Users (
    UserId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EmployeeId NVARCHAR(20) NOT NULL UNIQUE,
    Email NVARCHAR(255) NOT NULL UNIQUE,
    AzureAdObjectId UNIQUEIDENTIFIER NOT NULL UNIQUE,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    IsActive BIT DEFAULT 1,
    LastLogin DATETIME2 NULL,
    FailedAttempts INT DEFAULT 0,
    LockoutEnd DATETIME2 NULL,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NULL
);

-- Password reset table
CREATE TABLE PasswordReset (
    RequestId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId UNIQUEIDENTIFIER NOT NULL,
    Token NVARCHAR(100) NOT NULL UNIQUE,
    Expires DATETIME2 NOT NULL,
    IsUsed BIT DEFAULT 0,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    CONSTRAINT FK_PasswordReset_Users FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE
);

-- Login history table
CREATE TABLE LoginHistory (
    LogId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId UNIQUEIDENTIFIER NULL,
    AttemptedEmail NVARCHAR(255) NULL,
    AttemptedEmployeeId NVARCHAR(20) NULL,
    LoginTime DATETIME2 DEFAULT GETUTCDATE(),
    IPAddress NVARCHAR(50) NULL,
    Success BIT NOT NULL,
    FailureReason NVARCHAR(200) NULL,
    CONSTRAINT FK_LoginHistory_Users FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE SET NULL
);

-- =============================================
-- RBAC Tables
-- =============================================

-- Roles table
CREATE TABLE Roles (
    RoleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    RoleCode NVARCHAR(20) NOT NULL UNIQUE,
    Name NVARCHAR(50) NOT NULL UNIQUE,
    Description NVARCHAR(200) NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NULL
);

-- User-Role mapping table
CREATE TABLE UserRoles (
    UserRoleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId UNIQUEIDENTIFIER NOT NULL,
    RoleId UNIQUEIDENTIFIER NOT NULL,
    AssignedDate DATETIME2 DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserRoles_Users FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    CONSTRAINT FK_UserRoles_Roles FOREIGN KEY (RoleId) REFERENCES Roles(RoleId) ON DELETE CASCADE,
    CONSTRAINT UQ_UserRoles_UserId_RoleId UNIQUE (UserId, RoleId)
);

-- Entities table
CREATE TABLE Entities (
    EntityId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) NOT NULL UNIQUE,
    Description NVARCHAR(200) NULL,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE()
);

-- Modules table
CREATE TABLE Modules (
    ModuleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) NOT NULL,
    Description NVARCHAR(200) NULL,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE()
);

-- SubModules table
CREATE TABLE SubModules (
    SubModuleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ModuleId UNIQUEIDENTIFIER NOT NULL,
    Name NVARCHAR(50) NOT NULL,
    Description NVARCHAR(200) NULL,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    CONSTRAINT FK_SubModules_Modules FOREIGN KEY (ModuleId) REFERENCES Modules(ModuleId) ON DELETE CASCADE,
    CONSTRAINT UQ_SubModules_ModuleId_Name UNIQUE (ModuleId, Name)
);

-- Permissions table
CREATE TABLE Permissions (
    PermissionId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(20) NOT NULL UNIQUE,
    Description NVARCHAR(100) NULL,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE()
);

-- Role-Permission mapping table
CREATE TABLE RolePermissions (
    RolePermissionId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    RoleId UNIQUEIDENTIFIER NOT NULL,
    EntityId UNIQUEIDENTIFIER NULL,
    ModuleId UNIQUEIDENTIFIER NULL,
    SubModuleId UNIQUEIDENTIFIER NULL,
    PermissionId UNIQUEIDENTIFIER NOT NULL,
    Granted BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NULL,
    CONSTRAINT FK_RolePermissions_Roles FOREIGN KEY (RoleId) REFERENCES Roles(RoleId) ON DELETE CASCADE,
    CONSTRAINT FK_RolePermissions_Entities FOREIGN KEY (EntityId) REFERENCES Entities(EntityId),
    CONSTRAINT FK_RolePermissions_Modules FOREIGN KEY (ModuleId) REFERENCES Modules(ModuleId),
    CONSTRAINT FK_RolePermissions_SubModules FOREIGN KEY (SubModuleId) REFERENCES SubModules(SubModuleId),
    CONSTRAINT FK_RolePermissions_Permissions FOREIGN KEY (PermissionId) REFERENCES Permissions(PermissionId)
);

-- Audit logs table
CREATE TABLE AuditLogs (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    ClientId INT NULL,
    Action NVARCHAR(100) NOT NULL,
    Resource NVARCHAR(200) NULL,
    Details NVARCHAR(2000) NULL,
    Timestamp DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    IpAddress NVARCHAR(50) NULL,
    UserAgent NVARCHAR(500) NULL,
    Success BIT NOT NULL,
    ErrorMessage NVARCHAR(1000) NULL,
    CONSTRAINT FK_AuditLogs_Clients FOREIGN KEY (ClientId) REFERENCES Clients(Id) ON DELETE SET NULL
);

PRINT 'All tables created successfully.';

-- =============================================
-- Create Indexes for Performance
-- =============================================

-- B2B OAuth 2.0 Indexes
CREATE INDEX IX_Clients_ClientId ON Clients(ClientId);
CREATE INDEX IX_Clients_Status ON Clients(Status);
CREATE INDEX IX_Scopes_Name ON Scopes(Name);
CREATE INDEX IX_Scopes_IsActive ON Scopes(IsActive);
CREATE INDEX IX_AccessTokens_TokenId ON AccessTokens(TokenId);
CREATE INDEX IX_AccessTokens_ExpiresAt ON AccessTokens(ExpiresAt);
CREATE INDEX IX_AccessTokens_IsRevoked ON AccessTokens(IsRevoked);

-- User Management Indexes
CREATE INDEX IX_Users_EmployeeId ON Users(EmployeeId);
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_AzureAdObjectId ON Users(AzureAdObjectId);
CREATE INDEX IX_Users_IsActive ON Users(IsActive);
CREATE INDEX IX_LoginHistory_UserId ON LoginHistory(UserId);
CREATE INDEX IX_LoginHistory_LoginTime ON LoginHistory(LoginTime);
CREATE INDEX IX_PasswordReset_Token ON PasswordReset(Token);

-- RBAC Indexes
CREATE INDEX IX_Roles_Name ON Roles(Name);
CREATE INDEX IX_Roles_RoleCode ON Roles(RoleCode);
CREATE INDEX IX_Roles_IsActive ON Roles(IsActive);
CREATE INDEX IX_UserRoles_UserId ON UserRoles(UserId);
CREATE INDEX IX_UserRoles_RoleId ON UserRoles(RoleId);
CREATE INDEX IX_RolePermissions_RoleId ON RolePermissions(RoleId);
CREATE INDEX IX_RolePermissions_ModuleId ON RolePermissions(ModuleId);
CREATE INDEX IX_RolePermissions_SubModuleId ON RolePermissions(SubModuleId);
CREATE INDEX IX_RolePermissions_EntityId ON RolePermissions(EntityId);
CREATE INDEX IX_RolePermissions_PermissionId ON RolePermissions(PermissionId);

-- Audit Logs Indexes
CREATE INDEX IX_AuditLogs_Timestamp ON AuditLogs(Timestamp);
CREATE INDEX IX_AuditLogs_ClientId ON AuditLogs(ClientId);
CREATE INDEX IX_AuditLogs_Action ON AuditLogs(Action);

PRINT 'All indexes created successfully.';
