# Aviation Authentication Service - Database Setup

This directory contains all the necessary scripts and instructions to set up the MS SQL Server database for the Aviation Authentication Service.

## 📋 Prerequisites

- **SQL Server 2019 or later** (Express, Standard, or Enterprise)
- **PowerShell 5.1 or later** (for automated scripts)
- **.NET 8.0 SDK** (for Entity Framework migrations)
- **SQL Server Management Studio (SSMS)** (recommended for manual setup)

## 🚀 Quick Setup (Automated)

### Option 1: PowerShell Script (Recommended)

1. **Open PowerShell as Administrator**
2. **Navigate to the Database directory:**
   ```powershell
   cd Authentication-service/Database
   ```

3. **Run the setup script:**
   ```powershell
   # For SQL Server Authentication
   .\setup-database.ps1 -ServerName "YOUR_SERVER_NAME" -Username "YOUR_USERNAME" -Password "YOUR_PASSWORD"
   
   # For Windows Authentication
   .\setup-database.ps1 -ServerName "YOUR_SERVER_NAME" -UseWindowsAuth
   
   # For custom database name
   .\setup-database.ps1 -ServerName "YOUR_SERVER_NAME" -Username "YOUR_USERNAME" -Password "YOUR_PASSWORD" -DatabaseName "CustomDatabaseName"
   ```

### Option 2: Entity Framework Migrations

1. **Navigate to the project directory:**
   ```bash
   cd Authentication-service/Aviation.Authentication.Api
   ```

2. **Install EF Core tools:**
   ```bash
   dotnet tool install --global dotnet-ef
   ```

3. **Update connection string in appsettings.json:**
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=YOUR_SERVER;Database=AviationAuthentication;User Id=YOUR_USERNAME;Password=YOUR_PASSWORD;TrustServerCertificate=true;Encrypt=true;"
     }
   }
   ```

4. **Create and apply migration:**
   ```bash
   dotnet ef migrations add InitialCreate
   dotnet ef database update
   ```

## 🔧 Manual Setup

### Step 1: Create Database
Execute `01-CreateDatabase.sql` in SQL Server Management Studio or run:
```sql
sqlcmd -S YOUR_SERVER -U YOUR_USERNAME -P YOUR_PASSWORD -i 01-CreateDatabase.sql
```

### Step 2: Create Tables
Execute `02-CreateTables.sql`:
```sql
sqlcmd -S YOUR_SERVER -U YOUR_USERNAME -P YOUR_PASSWORD -d AviationAuthentication -i 02-CreateTables.sql
```

### Step 3: Seed Initial Data
Execute `03-SeedData.sql`:
```sql
sqlcmd -S YOUR_SERVER -U YOUR_USERNAME -P YOUR_PASSWORD -d AviationAuthentication -i 03-SeedData.sql
```

## 🧪 Testing Connection

Test your database connection:
```powershell
.\test-connection.ps1 -ServerName "YOUR_SERVER" -Username "YOUR_USERNAME" -Password "YOUR_PASSWORD"
```

## 📊 Database Schema

### Core Tables

| Table | Purpose |
|-------|---------|
| **Users** | Internal employee accounts with Azure AD integration |
| **Roles** | RBAC roles (Sales, Finance, Vendor, etc.) |
| **UserRoles** | User-role assignments |
| **Permissions** | CRUD permissions (Create, View, Update, Delete) |
| **RolePermissions** | Role-permission mappings |
| **Clients** | B2B OAuth 2.0 client applications |
| **Scopes** | API access scopes for B2B clients |
| **AccessTokens** | Issued JWT tokens tracking |
| **AuditLogs** | Security and operational audit trail |

### Seeded Data

#### Default Roles
- **ROLE-001**: Sales
- **ROLE-002**: Finance  
- **ROLE-003**: Vendor
- **ROLE-004**: Supply
- **ROLE-005**: Operational
- **ROLE-006**: Non-D operational
- **ROLE-007**: Sales Manager
- **ROLE-008**: Legal
- **ROLE-009**: Customer admin
- **ROLE-010**: Accounts
- **ROLE-011**: Tender Committee

#### Test Credentials
- **B2B Client ID**: `aviation_test_client`
- **B2B Client Secret**: `test_secret_123`
- **Admin User**: `<EMAIL>`

## 🔗 Connection String Examples

### SQL Server Authentication
```
Server=localhost;Database=AviationAuthentication;User Id=sa;Password=YourPassword123;TrustServerCertificate=true;Encrypt=true;
```

### Windows Authentication
```
Server=localhost;Database=AviationAuthentication;Integrated Security=true;TrustServerCertificate=true;
```

### Azure SQL Database
```
Server=your-server.database.windows.net;Database=AviationAuthentication;User Id=your-username;Password=your-password;Encrypt=true;
```

## 🛠️ Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check server name and port
   - Verify SQL Server is running
   - Check firewall settings

2. **Authentication Failed**
   - Verify username and password
   - Check SQL Server authentication mode
   - Ensure user has database creation permissions

3. **Database Already Exists**
   - Drop existing database or use different name
   - Update connection string accordingly

4. **Permission Denied**
   - Run PowerShell as Administrator
   - Ensure SQL user has dbcreator and sysadmin roles

### Verification Queries

```sql
-- Check if database exists
SELECT name FROM sys.databases WHERE name = 'AviationAuthentication';

-- Count tables
SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';

-- Check seeded data
SELECT COUNT(*) FROM Roles;
SELECT COUNT(*) FROM Scopes;
SELECT COUNT(*) FROM Users;
```

## 🔄 Updates and Migrations

For future schema changes:

1. **Create new migration:**
   ```bash
   dotnet ef migrations add MigrationName
   ```

2. **Apply migration:**
   ```bash
   dotnet ef database update
   ```

3. **Rollback migration:**
   ```bash
   dotnet ef database update PreviousMigrationName
   ```

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Test connection using the test script
4. Review SQL Server error logs
5. Check application logs for detailed error messages

## 🔒 Security Notes

- Change default passwords in production
- Use strong passwords for SQL Server accounts
- Enable SSL/TLS encryption for production
- Regularly backup the database
- Monitor audit logs for security events
- Rotate JWT signing keys periodically
