# =============================================
# Aviation Authentication Service Database Setup Script
# =============================================

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerName,
    
    [Parameter(Mandatory=$true)]
    [string]$Username,
    
    [Parameter(Mandatory=$true)]
    [string]$Password,
    
    [Parameter(Mandatory=$false)]
    [string]$DatabaseName = "AviationAuthentication",
    
    [Parameter(Mandatory=$false)]
    [switch]$UseWindowsAuth = $false
)

Write-Host "🚀 Starting Aviation Authentication Service Database Setup..." -ForegroundColor Green

# Build connection string
if ($UseWindowsAuth) {
    $connectionString = "Server=$ServerName;Database=master;Integrated Security=true;TrustServerCertificate=true;"
    $dbConnectionString = "Server=$ServerName;Database=$DatabaseName;Integrated Security=true;TrustServerCertificate=true;"
} else {
    $connectionString = "Server=$ServerName;Database=master;User Id=$Username;Password=$Password;TrustServerCertificate=true;Encrypt=true;"
    $dbConnectionString = "Server=$ServerName;Database=$DatabaseName;User Id=$Username;Password=$Password;TrustServerCertificate=true;Encrypt=true;"
}

try {
    # Test connection
    Write-Host "📡 Testing connection to SQL Server..." -ForegroundColor Yellow
    $testConnection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $testConnection.Open()
    $testConnection.Close()
    Write-Host "✅ Connection successful!" -ForegroundColor Green

    # Execute database creation script
    Write-Host "🗄️ Creating database..." -ForegroundColor Yellow
    $createDbScript = Get-Content -Path "01-CreateDatabase.sql" -Raw
    $createDbScript = $createDbScript.Replace("AviationAuthentication", $DatabaseName)
    
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $command = New-Object System.Data.SqlClient.SqlCommand($createDbScript, $connection)
    $connection.Open()
    $command.ExecuteNonQuery()
    $connection.Close()
    Write-Host "✅ Database created successfully!" -ForegroundColor Green

    # Execute table creation script
    Write-Host "📋 Creating tables..." -ForegroundColor Yellow
    $createTablesScript = Get-Content -Path "02-CreateTables.sql" -Raw
    $createTablesScript = $createTablesScript.Replace("AviationAuthentication", $DatabaseName)
    
    $connection = New-Object System.Data.SqlClient.SqlConnection($dbConnectionString)
    $command = New-Object System.Data.SqlClient.SqlCommand($createTablesScript, $connection)
    $connection.Open()
    $command.ExecuteNonQuery()
    $connection.Close()
    Write-Host "✅ Tables created successfully!" -ForegroundColor Green

    # Execute seed data script
    Write-Host "🌱 Seeding initial data..." -ForegroundColor Yellow
    $seedDataScript = Get-Content -Path "03-SeedData.sql" -Raw
    $seedDataScript = $seedDataScript.Replace("AviationAuthentication", $DatabaseName)
    
    $connection = New-Object System.Data.SqlClient.SqlConnection($dbConnectionString)
    $command = New-Object System.Data.SqlClient.SqlCommand($seedDataScript, $connection)
    $connection.Open()
    $command.ExecuteNonQuery()
    $connection.Close()
    Write-Host "✅ Initial data seeded successfully!" -ForegroundColor Green

    Write-Host ""
    Write-Host "🎉 Database setup completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📊 Database Information:" -ForegroundColor Cyan
    Write-Host "   Server: $ServerName" -ForegroundColor White
    Write-Host "   Database: $DatabaseName" -ForegroundColor White
    Write-Host "   Authentication: $(if ($UseWindowsAuth) { 'Windows Authentication' } else { 'SQL Server Authentication' })" -ForegroundColor White
    Write-Host ""
    Write-Host "🔑 Test Credentials:" -ForegroundColor Cyan
    Write-Host "   B2B Client ID: aviation_test_client" -ForegroundColor White
    Write-Host "   B2B Client Secret: test_secret_123" -ForegroundColor White
    Write-Host "   Admin User: <EMAIL>" -ForegroundColor White
    Write-Host ""
    Write-Host "🔗 Connection String for appsettings.json:" -ForegroundColor Cyan
    Write-Host "   $dbConnectionString" -ForegroundColor Yellow

} catch {
    Write-Host "❌ Error during database setup: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ Setup completed! You can now start the Authentication Service." -ForegroundColor Green
