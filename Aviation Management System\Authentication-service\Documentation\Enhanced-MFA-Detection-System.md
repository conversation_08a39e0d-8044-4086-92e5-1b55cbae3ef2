# Enhanced MFA Detection and Authentication System

## Overview

The Aviation Management System's Authentication Service has been enhanced with sophisticated Multi-Factor Authentication (MFA) detection and specific error handling capabilities. This system provides detailed feedback about authentication requirements and failures, helping users understand exactly what authentication method they need to use.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client App    │───▶│  Auth Controller │───▶│  Azure AD       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │  Error Parser    │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │  Database Logger │
                       └──────────────────┘
```

## Key Components

### 1. Enhanced AuthController (`AuthController.cs`)

The main authentication endpoint with improved error handling:

**Endpoint:** `POST /api/auth/login`

**Request:**
```json
{
  "emailOrEmployeeId": "<EMAIL>",
  "password": "userpassword"
}
```

**Enhanced Response Examples:**

**MFA Required:**
```json
{
  "error": "Multi-Factor Authentication (MFA) is required for this account",
  "errorCode": "MFA_REQUIRED",
  "field": "authentication_method",
  "message": "Account '<EMAIL>' has MFA enabled. Please use the OAuth2 authentication flow instead of password authentication.",
  "requiresOAuth": true,
  "suggestedAction": "Use OAuth2 flow for authentication"
}
```

**Invalid Password:**
```json
{
  "error": "Invalid credentials",
  "errorCode": "INVALID_CREDENTIALS",
  "field": "password",
  "message": "The password provided for '<EMAIL>' is incorrect. Please check your password and try again.",
  "requiresOAuth": false,
  "suggestedAction": "Check password"
}
```

### 2. Azure AD Service (`AzureAdService.cs`)

Enhanced with sophisticated error parsing capabilities:

#### Error Detection Methods:

- `ParseAzureAdError()` - Main error parsing logic
- `IsMfaRequiredError()` - Detects MFA requirements
- `IsConditionalAccessError()` - Detects Conditional Access policies
- `IsAccountDisabledError()` - Detects disabled/locked accounts
- `IsPasswordExpiredError()` - Detects expired passwords

#### Supported Azure AD Error Codes:

| Error Type | Error Codes | Description |
|------------|-------------|-------------|
| MFA Required | 50076, 50079, 50074, 50158 | Multi-Factor Authentication is required |
| Conditional Access | 53003, 53000, 53001, 53002, 53004 | Conditional Access policies block access |
| Account Disabled | 50057, 50058, 50034, 50053 | Account is disabled or locked |
| Password Expired | 50055, 50056, 50173 | Password has expired |

### 3. Authentication Requirements Check

**New Endpoint:** `POST /api/auth/check-auth-requirements`

This endpoint allows checking authentication requirements without attempting login:

**Request:**
```json
{
  "emailOrEmployeeId": "<EMAIL>"
}
```

**Response:**
```json
{
  "email": "<EMAIL>",
  "employeeId": "EMP001",
  "fullName": "User Name",
  "authenticationMethod": "OAuth2 with MFA",
  "requiresMfa": true,
  "requiresConditionalAccess": false,
  "supportsPasswordAuth": false,
  "recommendedFlow": "oauth2",
  "message": "Account '<EMAIL>' has Multi-Factor Authentication (MFA) enabled. Use OAuth2 flow for authentication."
}
```

## Authentication Flow

### 1. Standard Password Authentication Flow

```mermaid
graph TD
    A[Client Login Request] --> B[Validate Input]
    B --> C[Find User in Database]
    C --> D[Check User Status]
    D --> E[Authenticate with Azure AD]
    E --> F{Azure AD Response}
    F -->|Success| G[Generate JWT Token]
    F -->|Error| H[Parse Azure AD Error]
    H --> I[Determine Error Type]
    I --> J[Return Specific Error]
    G --> K[Return Success Response]
```

### 2. Error Detection Flow

```mermaid
graph TD
    A[Azure AD Error Response] --> B[Parse JSON Response]
    B --> C[Extract Error Codes]
    C --> D{Check Error Type}
    D -->|50076, 50079, etc.| E[MFA Required]
    D -->|53003, 53000, etc.| F[Conditional Access]
    D -->|50057, 50058, etc.| G[Account Disabled]
    D -->|50055, 50056, etc.| H[Password Expired]
    D -->|Other| I[Generic Auth Failed]
    E --> J[Return MFA Error Response]
    F --> K[Return Conditional Access Error]
    G --> L[Return Account Disabled Error]
    H --> M[Return Password Expired Error]
    I --> N[Return Generic Error]
```

## Database Integration

### Login History Logging

The system logs all authentication attempts with enhanced failure reasons:

**Table:** `LoginHistory`
- `FailureReason` (NVARCHAR(200)) - Short, database-friendly error messages
- Automatic failed attempt counting and account lockout

**Short Failure Reasons:**
- "MFA required - use OAuth2"
- "Conditional Access - use OAuth2"
- "Account disabled in Azure AD"
- "Password expired"
- "Invalid credentials"

### Account Lockout Logic

- Automatic lockout after 5 failed attempts
- 30-minute lockout duration
- Lockout status checked before authentication

## Configuration

### appsettings.json Configuration

```json
{
  "AzureAd": {
    "TenantId": "your-tenant-id",
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "Instance": "https://login.microsoftonline.com/",
    "GraphApiUrl": "https://graph.microsoft.com/",
    "Scopes": ["User.Read.All", "Directory.Read.All", "Group.Read.All"]
  }
}
```

## Error Response Structure

All authentication errors follow a consistent structure:

```json
{
  "error": "Human-readable error message",
  "errorCode": "MACHINE_READABLE_CODE",
  "field": "specific_field_with_issue",
  "message": "Detailed explanation with context",
  "requiresOAuth": boolean,
  "suggestedAction": "What the user should do next"
}
```

### Error Code Reference

| Error Code | Field | Description | Requires OAuth |
|------------|-------|-------------|----------------|
| `MFA_REQUIRED` | `authentication_method` | MFA is enabled for account | Yes |
| `CONDITIONAL_ACCESS_REQUIRED` | `authentication_method` | Conditional Access policies apply | Yes |
| `ACCOUNT_DISABLED` | `email/employeeId` | Account is disabled in Azure AD | No |
| `PASSWORD_EXPIRED` | `password` | Password has expired | No |
| `INVALID_CREDENTIALS` | `password` | Incorrect password | No |
| `USER_NOT_FOUND` | `email/employeeId` | User not found in database | No |
| `ACCOUNT_INACTIVE` | `account` | Account is inactive locally | No |
| `ACCOUNT_LOCKED` | `account` | Account is locked due to failed attempts | No |

## OAuth2 Flow for MFA

When MFA is detected, users should use the OAuth2 flow:

### 1. Get Authorization URL
```
GET /api/auth/azure-auth-url
```

### 2. User Authentication
User is redirected to Azure AD for authentication with MFA

### 3. Handle Callback
```
POST /api/auth/azure-callback
{
  "code": "authorization_code",
  "state": "state_value",
  "redirectUri": "callback_url"
}
```

## Security Features

### 1. Dual Authentication Requirement
- Users must exist in both local database AND Azure AD
- Email validation ensures consistency between systems

### 2. Account Protection
- Automatic lockout after failed attempts
- Detailed audit logging of all authentication attempts
- IP address tracking for security monitoring

### 3. Error Information Security
- Sensitive information is not exposed in error messages
- Generic messages for security-sensitive scenarios
- Detailed logging for administrative review

## Monitoring and Logging

### 1. Structured Logging
All authentication events are logged with:
- User identification (email/employee ID)
- IP address
- Timestamp
- Success/failure status
- Detailed failure reasons
- Azure AD error codes

### 2. Health Checks
- Database connectivity
- Azure AD service availability
- Authentication service health

### 3. Metrics
- Authentication success/failure rates
- MFA detection rates
- Account lockout frequency
- Error type distribution

## Troubleshooting Guide

### Common Issues and Solutions

1. **"MFA Required" Error**
   - **Cause:** User account has MFA enabled in Azure AD
   - **Solution:** Use OAuth2 authentication flow
   - **Code:** `MFA_REQUIRED`

2. **"Invalid Credentials" Error**
   - **Cause:** Incorrect password
   - **Solution:** Verify password is correct
   - **Code:** `INVALID_CREDENTIALS`

3. **"Account Disabled" Error**
   - **Cause:** Account disabled in Azure AD
   - **Solution:** Contact administrator
   - **Code:** `ACCOUNT_DISABLED`

4. **"Account Locked" Error**
   - **Cause:** Too many failed login attempts
   - **Solution:** Wait for lockout period or contact administrator
   - **Code:** `ACCOUNT_LOCKED`

### Network Connectivity Issues

If Azure AD is unreachable:
- Check firewall settings
- Verify DNS resolution
- Test network connectivity to `login.microsoftonline.com`
- Review proxy configurations

## API Testing

### Test MFA Detection
```bash
curl -X POST http://localhost:5295/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "emailOrEmployeeId": "<EMAIL>",
    "password": "password123"
  }'
```

### Test Authentication Requirements Check
```bash
curl -X POST http://localhost:5295/api/auth/check-auth-requirements \
  -H "Content-Type: application/json" \
  -d '{
    "emailOrEmployeeId": "<EMAIL>"
  }'
```

## Future Enhancements

### Planned Features
1. **Risk-based Authentication**
   - Location-based risk assessment
   - Device fingerprinting
   - Behavioral analysis

2. **Enhanced MFA Support**
   - FIDO2/WebAuthn support
   - SMS/Phone call backup
   - Hardware token integration

3. **Advanced Monitoring**
   - Real-time security alerts
   - Anomaly detection
   - Compliance reporting

### Integration Opportunities
1. **Identity Providers**
   - SAML 2.0 support
   - OpenID Connect
   - Active Directory Federation Services

2. **Security Tools**
   - SIEM integration
   - Threat intelligence feeds
   - Security orchestration platforms

## Conclusion

The enhanced MFA detection system provides a robust, user-friendly authentication experience while maintaining high security standards. The detailed error reporting helps users understand authentication requirements and guides them to the appropriate authentication method, significantly improving the user experience while maintaining security compliance.
