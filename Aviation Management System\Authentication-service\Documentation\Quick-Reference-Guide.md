# Quick Reference Guide - Enhanced MFA Detection System

## API Endpoints

### 1. User Login
```
POST /api/auth/login
Content-Type: application/json

{
  "emailOrEmployeeId": "<EMAIL>",
  "password": "userpassword"
}
```

**Success Response (200):**
```json
{
  "accessToken": "jwt-token-here",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "user": {
    "userId": "guid",
    "employeeId": "EMP001",
    "email": "<EMAIL>",
    "fullName": "User Name",
    "isActive": true,
    "roles": [...]
  }
}
```

**Error Responses (401):**

**MFA Required:**
```json
{
  "error": "Multi-Factor Authentication (MFA) is required for this account",
  "errorCode": "MFA_REQUIRED",
  "field": "authentication_method",
  "message": "Account '<EMAIL>' has MFA enabled. Please use the OAuth2 authentication flow instead of password authentication.",
  "requiresOAuth": true,
  "suggestedAction": "Use OAuth2 flow for authentication"
}
```

**Invalid Password:**
```json
{
  "error": "Invalid credentials",
  "errorCode": "INVALID_CREDENTIALS",
  "field": "password",
  "message": "The password provided for '<EMAIL>' is incorrect. Please check your password and try again.",
  "requiresOAuth": false,
  "suggestedAction": "Check password"
}
```

**Account Locked:**
```json
{
  "error": "Account is locked",
  "errorCode": "ACCOUNT_LOCKED",
  "field": "account",
  "message": "Account is locked until 2024-01-15 10:30:00 UTC due to multiple failed login attempts.",
  "lockoutEnd": "2024-01-15T10:30:00Z"
}
```

### 2. Check Authentication Requirements
```
POST /api/auth/check-auth-requirements
Content-Type: application/json

{
  "emailOrEmployeeId": "<EMAIL>"
}
```

**Response (200):**
```json
{
  "email": "<EMAIL>",
  "employeeId": "EMP001",
  "fullName": "User Name",
  "authenticationMethod": "OAuth2 with MFA",
  "requiresMfa": true,
  "requiresConditionalAccess": false,
  "supportsPasswordAuth": false,
  "recommendedFlow": "oauth2",
  "message": "Account '<EMAIL>' has Multi-Factor Authentication (MFA) enabled. Use OAuth2 flow for authentication."
}
```

### 3. OAuth2 Flow (for MFA users)

**Step 1: Get Authorization URL**
```
GET /api/auth/azure-auth-url
```

**Response:**
```json
{
  "authUrl": "https://login.microsoftonline.com/...",
  "state": "random-state-value",
  "codeVerifier": "pkce-code-verifier"
}
```

**Step 2: Handle Callback**
```
POST /api/auth/azure-callback
Content-Type: application/json

{
  "code": "authorization-code-from-azure",
  "state": "state-value-from-step-1",
  "redirectUri": "your-callback-url"
}
```

## Error Codes Reference

| Error Code | HTTP Status | Description | Field | Requires OAuth |
|------------|-------------|-------------|-------|----------------|
| `MFA_REQUIRED` | 401 | MFA is enabled for account | `authentication_method` | Yes |
| `CONDITIONAL_ACCESS_REQUIRED` | 401 | Conditional Access policies apply | `authentication_method` | Yes |
| `INVALID_CREDENTIALS` | 401 | Incorrect password | `password` | No |
| `USER_NOT_FOUND` | 401 | User not found in database | `email/employeeId` | No |
| `ACCOUNT_INACTIVE` | 401 | Account is inactive locally | `account` | No |
| `ACCOUNT_LOCKED` | 401 | Account locked due to failed attempts | `account` | No |
| `ACCOUNT_DISABLED` | 401 | Account disabled in Azure AD | `email/employeeId` | No |
| `PASSWORD_EXPIRED` | 401 | Password has expired | `password` | No |
| `MISSING_IDENTIFIER` | 400 | Email/Employee ID not provided | `emailOrEmployeeId` | No |
| `MISSING_PASSWORD` | 400 | Password not provided | `password` | No |

## Azure AD Error Codes Mapping

| Azure AD Error Code | Our Error Code | Description |
|---------------------|----------------|-------------|
| 50076, 50079, 50074, 50158 | `MFA_REQUIRED` | Multi-Factor Authentication required |
| 53003, 53000, 53001, 53002, 53004 | `CONDITIONAL_ACCESS_REQUIRED` | Conditional Access policies |
| 50057, 50058, 50034, 50053 | `ACCOUNT_DISABLED` | Account disabled or locked |
| 50055, 50056, 50173 | `PASSWORD_EXPIRED` | Password expired |
| Other | `AZURE_AUTH_FAILED` | Generic authentication failure |

## Configuration

### appsettings.json
```json
{
  "AzureAd": {
    "TenantId": "your-tenant-id",
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "Instance": "https://login.microsoftonline.com/",
    "GraphApiUrl": "https://graph.microsoft.com/",
    "Scopes": ["User.Read.All", "Directory.Read.All", "Group.Read.All"]
  },
  "JWT": {
    "SecretKey": "your-jwt-secret-key",
    "Issuer": "https://auth.aviation-management.com",
    "Audience": "aviation-api",
    "ExpirationMinutes": 60
  }
}
```

### Database Schema

**LoginHistory Table:**
```sql
CREATE TABLE LoginHistory (
    LogId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId UNIQUEIDENTIFIER NULL,
    AttemptedEmail NVARCHAR(255) NULL,
    AttemptedEmployeeId NVARCHAR(20) NULL,
    LoginTime DATETIME2 DEFAULT GETUTCDATE(),
    IPAddress NVARCHAR(50) NULL,
    Success BIT NOT NULL,
    FailureReason NVARCHAR(200) NULL,
    CONSTRAINT FK_LoginHistory_Users FOREIGN KEY (UserId) REFERENCES Users(UserId)
);
```

**Users Table (relevant fields):**
```sql
ALTER TABLE Users ADD 
    FailedAttempts INT DEFAULT 0,
    LockoutEnd DATETIME2 NULL,
    LastLogin DATETIME2 NULL;
```

## Client Implementation Examples

### JavaScript/TypeScript
```typescript
interface LoginRequest {
  emailOrEmployeeId: string;
  password: string;
}

interface LoginResponse {
  accessToken: string;
  tokenType: string;
  expiresIn: number;
  user: UserInfo;
}

interface ErrorResponse {
  error: string;
  errorCode: string;
  field: string;
  message: string;
  requiresOAuth: boolean;
  suggestedAction: string;
}

async function login(credentials: LoginRequest): Promise<LoginResponse> {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (response.ok) {
      return await response.json() as LoginResponse;
    }

    const error = await response.json() as ErrorResponse;
    
    if (error.requiresOAuth) {
      // Redirect to OAuth2 flow
      window.location.href = await getOAuth2Url();
    } else {
      // Show specific error message
      showError(error.message, error.field);
    }
    
    throw new Error(error.message);
  } catch (err) {
    console.error('Login failed:', err);
    throw err;
  }
}

async function checkAuthRequirements(emailOrEmployeeId: string) {
  const response = await fetch('/api/auth/check-auth-requirements', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ emailOrEmployeeId }),
  });

  return await response.json();
}
```

### C# Client
```csharp
public class AuthClient
{
    private readonly HttpClient _httpClient;

    public AuthClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<LoginResponse> LoginAsync(LoginRequest request)
    {
        var response = await _httpClient.PostAsJsonAsync("/api/auth/login", request);
        
        if (response.IsSuccessStatusCode)
        {
            return await response.Content.ReadFromJsonAsync<LoginResponse>();
        }

        var error = await response.Content.ReadFromJsonAsync<ErrorResponse>();
        
        if (error.RequiresOAuth)
        {
            // Handle OAuth2 flow
            throw new MfaRequiredException(error.Message);
        }

        throw new AuthenticationException(error.Message);
    }

    public async Task<AuthRequirementsResponse> CheckAuthRequirementsAsync(string emailOrEmployeeId)
    {
        var request = new CheckAuthRequirementsRequest { EmailOrEmployeeId = emailOrEmployeeId };
        var response = await _httpClient.PostAsJsonAsync("/api/auth/check-auth-requirements", request);
        
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<AuthRequirementsResponse>();
    }
}
```

## Testing Commands

### cURL Examples

**Test Login:**
```bash
curl -X POST http://localhost:5295/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "emailOrEmployeeId": "<EMAIL>",
    "password": "password123"
  }'
```

**Test Auth Requirements:**
```bash
curl -X POST http://localhost:5295/api/auth/check-auth-requirements \
  -H "Content-Type: application/json" \
  -d '{
    "emailOrEmployeeId": "<EMAIL>"
  }'
```

**Test OAuth2 URL:**
```bash
curl -X GET http://localhost:5295/api/auth/azure-auth-url
```

### PowerShell Examples

**Test Login:**
```powershell
$body = @{
    emailOrEmployeeId = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5295/api/auth/login" `
  -Method POST `
  -ContentType "application/json" `
  -Body $body
```

## Troubleshooting

### Common Issues

1. **"Address already in use" error**
   ```bash
   # Kill existing processes
   taskkill /F /IM Aviation.Authentication.Api.exe
   
   # Use different port
   dotnet run --urls "http://localhost:5296"
   ```

2. **Azure AD connectivity issues**
   ```bash
   # Test connectivity
   curl -I https://login.microsoftonline.com
   
   # Check DNS resolution
   nslookup login.microsoftonline.com
   ```

3. **Database connection issues**
   - Verify connection string in appsettings.json
   - Ensure SQL Server is running
   - Check database permissions

4. **JWT token issues**
   - Verify JWT secret key is configured
   - Check token expiration settings
   - Validate issuer and audience

### Logging

**Enable detailed logging in appsettings.json:**
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Aviation.Authentication.Api": "Debug",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

**Log file locations:**
- `logs/authentication-service-YYYYMMDD.txt`
- Console output during development

### Health Checks

**Check API health:**
```bash
curl http://localhost:5295/health
```

**Check database connectivity:**
```bash
curl http://localhost:5295/health/db
```

## Deployment Checklist

### Pre-deployment
- [ ] Update Azure AD configuration
- [ ] Set production JWT secret key
- [ ] Configure production database connection
- [ ] Update CORS settings for production domains
- [ ] Set up SSL certificates
- [ ] Configure logging for production

### Post-deployment
- [ ] Test all authentication flows
- [ ] Verify MFA detection works
- [ ] Check error responses are appropriate
- [ ] Monitor login success/failure rates
- [ ] Verify audit logging is working

### Monitoring
- [ ] Set up alerts for high failure rates
- [ ] Monitor account lockout frequency
- [ ] Track MFA detection accuracy
- [ ] Monitor API response times
- [ ] Set up security event notifications

## Support Contacts

- **Development Team:** <EMAIL>
- **Security Team:** <EMAIL>
- **Infrastructure:** <EMAIL>

## Version History

| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 2024-01-01 | Initial implementation |
| 1.1.0 | 2024-01-15 | Enhanced MFA detection |
| 1.2.0 | 2024-02-01 | Added auth requirements check |

---

*This guide covers the essential information for implementing and using the Enhanced MFA Detection System. For detailed technical information, refer to the Technical Implementation Guide.*
