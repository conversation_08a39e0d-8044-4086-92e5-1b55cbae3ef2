# Technical Implementation Guide - Enhanced MFA Detection

## Code Architecture Overview

This document provides detailed technical information about the implementation of the enhanced MFA detection system in the Aviation Authentication Service.

## Core Components

### 1. AuthController.cs - Enhanced Login Method

#### Key Method: `Login(UserLoginRequest request)`

```csharp
[HttpPost("login")]
public async Task<IActionResult> Login([FromBody] UserLoginRequest request)
{
    // Step 0: Input Validation
    if (string.IsNullOrWhiteSpace(request.EmailOrEmployeeId))
    {
        return BadRequest(new { 
            error = "Email or Employee ID is required",
            errorCode = "MISSING_IDENTIFIER",
            field = "emailOrEmployeeId",
            message = "Please provide either an email address or employee ID."
        });
    }

    // Step 1: Database User Lookup
    User? user = null;
    var isEmail = request.EmailOrEmployeeId.Contains("@");
    
    if (isEmail)
        user = await _userService.GetUserByEmailAsync(request.EmailOrEmployeeId);
    else
        user = await _userService.GetUserByEmployeeIdAsync(request.EmailOrEmployeeId);

    // Step 2: User Validation
    if (user == null)
    {
        await _userService.LogLoginAttemptAsync(null, attemptedEmail, attemptedEmployeeId, ipAddress, false, "User not found");
        return Unauthorized(new { 
            error = "User not found",
            errorCode = "USER_NOT_FOUND",
            field = isEmail ? "email" : "employeeId",
            message = $"No user found with {(isEmail ? "email" : "employee ID")} '{request.EmailOrEmployeeId}'. Please check your credentials."
        });
    }

    // Step 3: Account Status Validation
    if (!user.IsActive)
    {
        await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, "Account inactive");
        return Unauthorized(new { 
            error = "Account is inactive",
            errorCode = "ACCOUNT_INACTIVE",
            field = "account",
            message = $"Account for '{request.EmailOrEmployeeId}' is inactive. Please contact your administrator."
        });
    }

    // Step 4: Lockout Check
    if (user.IsLockedOut)
    {
        var lockoutMessage = user.LockoutEnd.HasValue 
            ? $"Account is locked until {user.LockoutEnd.Value:yyyy-MM-dd HH:mm:ss} UTC due to multiple failed login attempts."
            : "Account is locked due to multiple failed login attempts.";
            
        return Unauthorized(new { 
            error = "Account is locked",
            errorCode = "ACCOUNT_LOCKED",
            field = "account",
            message = lockoutMessage,
            lockoutEnd = user.LockoutEnd
        });
    }

    // Step 5: Azure AD Authentication
    var azureAuthResult = await _azureAdService.AuthenticateUserAsync(user.Email, request.Password);
    if (!azureAuthResult.IsSuccess)
    {
        // Create short failure reason for database (max 200 chars)
        var shortFailureReason = CreateShortFailureReason(azureAuthResult);
        
        // Log failed attempt (automatically increments failed attempts and handles lockout)
        await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, shortFailureReason);
        
        // Return detailed error response
        var errorResponse = CreateDetailedErrorResponse(azureAuthResult, request.EmailOrEmployeeId);
        return Unauthorized(errorResponse);
    }

    // Step 6: Success - Generate JWT and return
    var permissions = await _userService.GetUserPermissionsAsync(user.UserId);
    var accessToken = await _userTokenService.GenerateUserTokenAsync(user, permissions);
    
    // Update user login info
    user.LastLogin = DateTime.UtcNow;
    user.FailedAttempts = 0;
    await _userService.UpdateUserAsync(user.UserId, new UpdateUserRequest());
    
    // Log successful login
    await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, true);
    
    return Ok(new UserLoginResponse { /* ... */ });
}
```

#### Helper Methods

```csharp
/// <summary>
/// Create a short failure reason for database logging (max 200 characters)
/// </summary>
private static string CreateShortFailureReason(AzureAdAuthResult azureAuthResult)
{
    var reason = azureAuthResult.ErrorCode switch
    {
        "MFA_REQUIRED" => "MFA required - use OAuth2",
        "CONDITIONAL_ACCESS_REQUIRED" => "Conditional Access - use OAuth2",
        "ACCOUNT_DISABLED" => "Account disabled in Azure AD",
        "PASSWORD_EXPIRED" => "Password expired",
        "AZURE_AUTH_FAILED" => "Invalid credentials",
        _ => azureAuthResult.ErrorCode ?? "Authentication failed"
    };

    // Ensure it fits within 200 characters
    return reason.Length > 200 ? reason.Substring(0, 197) + "..." : reason;
}

/// <summary>
/// Create detailed error response with specific field validation
/// </summary>
private object CreateDetailedErrorResponse(AzureAdAuthResult azureAuthResult, string emailOrEmployeeId)
{
    var isEmail = emailOrEmployeeId.Contains("@");
    
    return azureAuthResult.ErrorCode switch
    {
        "MFA_REQUIRED" => new
        {
            error = "Multi-Factor Authentication (MFA) is required for this account",
            errorCode = "MFA_REQUIRED",
            field = "authentication_method",
            message = $"Account '{emailOrEmployeeId}' has MFA enabled. Please use the OAuth2 authentication flow instead of password authentication.",
            requiresOAuth = true,
            suggestedAction = "Use OAuth2 flow for authentication"
        },
        "CONDITIONAL_ACCESS_REQUIRED" => new
        {
            error = "Conditional Access policies require additional authentication",
            errorCode = "CONDITIONAL_ACCESS_REQUIRED", 
            field = "authentication_method",
            message = $"Account '{emailOrEmployeeId}' is subject to Conditional Access policies. Please use the OAuth2 authentication flow.",
            requiresOAuth = true,
            suggestedAction = "Use OAuth2 flow for authentication"
        },
        "ACCOUNT_DISABLED" => new
        {
            error = "Account is disabled",
            errorCode = "ACCOUNT_DISABLED",
            field = isEmail ? "email" : "employeeId",
            message = $"Account '{emailOrEmployeeId}' is disabled in Azure AD. Please contact your administrator.",
            requiresOAuth = false,
            suggestedAction = "Contact administrator"
        },
        "PASSWORD_EXPIRED" => new
        {
            error = "Password has expired",
            errorCode = "PASSWORD_EXPIRED",
            field = "password",
            message = $"Password for account '{emailOrEmployeeId}' has expired. Please reset your password.",
            requiresOAuth = false,
            suggestedAction = "Reset password"
        },
        "AZURE_AUTH_FAILED" => new
        {
            error = "Invalid credentials",
            errorCode = "INVALID_CREDENTIALS",
            field = "password",
            message = $"The password provided for '{emailOrEmployeeId}' is incorrect. Please check your password and try again.",
            requiresOAuth = false,
            suggestedAction = "Check password"
        },
        _ => new
        {
            error = "Authentication failed",
            errorCode = azureAuthResult.ErrorCode ?? "AUTH_FAILED",
            field = "credentials",
            message = azureAuthResult.ErrorMessage ?? $"Authentication failed for '{emailOrEmployeeId}'. Please check your credentials.",
            requiresOAuth = false,
            suggestedAction = "Check credentials"
        }
    };
}
```

### 2. AzureAdService.cs - Enhanced Error Parsing

#### Main Authentication Method

```csharp
public async Task<AzureAdAuthResult> AuthenticateUserAsync(string email, string password)
{
    try
    {
        _logger.LogInformation("Authenticating user {Email} with Azure AD", email);
        
        // Use Resource Owner Password Credentials (ROPC) flow
        var httpClient = new HttpClient();
        var tokenEndpoint = $"https://login.microsoftonline.com/{_azureAdOptions.TenantId}/oauth2/v2.0/token";
        
        var requestBody = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("client_id", _azureAdOptions.ClientId),
            new KeyValuePair<string, string>("client_secret", _azureAdOptions.ClientSecret),
            new KeyValuePair<string, string>("scope", "https://graph.microsoft.com/.default"),
            new KeyValuePair<string, string>("username", email),
            new KeyValuePair<string, string>("password", password),
            new KeyValuePair<string, string>("grant_type", "password")
        });

        var response = await httpClient.PostAsync(tokenEndpoint, requestBody);
        var responseContent = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogWarning("Azure AD authentication failed for user {Email}. Status: {Status}, Response: {Response}",
                email, response.StatusCode, responseContent);

            // Parse error response to detect MFA requirements
            var errorResult = ParseAzureAdError(responseContent, email);
            return errorResult;
        }

        // Parse successful token response
        var tokenResponse = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);
        var accessToken = tokenResponse?["access_token"]?.ToString();

        if (string.IsNullOrEmpty(accessToken))
        {
            return new AzureAdAuthResult
            {
                IsSuccess = false,
                ErrorMessage = "Failed to obtain access token",
                ErrorCode = "NO_ACCESS_TOKEN"
            };
        }

        // Get user information using the access token
        var azureUser = await ValidateTokenAsync(accessToken);
        if (azureUser == null)
        {
            return new AzureAdAuthResult
            {
                IsSuccess = false,
                ErrorMessage = "Failed to get user information from Azure AD",
                ErrorCode = "USER_INFO_FAILED"
            };
        }

        _logger.LogInformation("Successfully authenticated user {Email} with Azure AD", email);

        return new AzureAdAuthResult
        {
            IsSuccess = true,
            User = azureUser,
            AccessToken = accessToken
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error authenticating user {Email} with Azure AD", email);
        return new AzureAdAuthResult
        {
            IsSuccess = false,
            ErrorMessage = "Authentication service error",
            ErrorCode = "SERVICE_ERROR"
        };
    }
}
```

#### Error Parsing Logic

```csharp
/// <summary>
/// Parse Azure AD error response to detect specific error conditions like MFA requirements
/// </summary>
private AzureAdAuthResult ParseAzureAdError(string errorResponse, string email)
{
    try
    {
        var errorData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(errorResponse);
        
        if (errorData != null)
        {
            var error = errorData.ContainsKey("error") ? errorData["error"]?.ToString() : "";
            var errorDescription = errorData.ContainsKey("error_description") ? errorData["error_description"]?.ToString() : "";
            var errorCodes = new List<string>();

            // Extract error codes if present
            if (errorData.ContainsKey("error_codes") && errorData["error_codes"] is System.Text.Json.JsonElement errorCodesElement)
            {
                if (errorCodesElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                {
                    foreach (var code in errorCodesElement.EnumerateArray())
                    {
                        if (code.ValueKind == System.Text.Json.JsonValueKind.Number)
                        {
                            errorCodes.Add(code.GetInt32().ToString());
                        }
                    }
                }
            }

            _logger.LogInformation("Azure AD Error Details - Error: {Error}, Description: {Description}, Codes: {Codes}",
                error, errorDescription, string.Join(", ", errorCodes));

            // Check for MFA-related errors
            if (IsMfaRequiredError(error, errorDescription, errorCodes))
            {
                _logger.LogWarning("MFA is required for user {Email}", email);
                return new AzureAdAuthResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"Multi-Factor Authentication (MFA) is enabled for account '{email}'. Please use the OAuth2 flow for authentication with MFA support.",
                    ErrorCode = "MFA_REQUIRED"
                };
            }

            // Check for conditional access policy errors
            if (IsConditionalAccessError(error, errorDescription, errorCodes))
            {
                _logger.LogWarning("Conditional Access policy blocks authentication for user {Email}", email);
                return new AzureAdAuthResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"Account '{email}' is subject to Conditional Access policies that require additional authentication. Please use the OAuth2 flow.",
                    ErrorCode = "CONDITIONAL_ACCESS_REQUIRED"
                };
            }

            // Check for account disabled/locked errors
            if (IsAccountDisabledError(error, errorDescription, errorCodes))
            {
                _logger.LogWarning("Account is disabled or locked for user {Email}", email);
                return new AzureAdAuthResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"Account '{email}' is disabled or locked in Azure AD. Please contact your administrator.",
                    ErrorCode = "ACCOUNT_DISABLED"
                };
            }

            // Check for password expired errors
            if (IsPasswordExpiredError(error, errorDescription, errorCodes))
            {
                _logger.LogWarning("Password expired for user {Email}", email);
                return new AzureAdAuthResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"Password for account '{email}' has expired. Please reset your password.",
                    ErrorCode = "PASSWORD_EXPIRED"
                };
            }

            // Generic error with specific description if available
            if (!string.IsNullOrEmpty(errorDescription))
            {
                return new AzureAdAuthResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"Azure AD authentication failed for '{email}': {errorDescription}",
                    ErrorCode = error?.ToUpper() ?? "AZURE_AUTH_FAILED"
                };
            }
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error parsing Azure AD error response");
    }

    // Fallback generic error
    return new AzureAdAuthResult
    {
        IsSuccess = false,
        ErrorMessage = $"Invalid credentials or user '{email}' not found in Azure AD",
        ErrorCode = "AZURE_AUTH_FAILED"
    };
}
```

#### Error Detection Helper Methods

```csharp
/// <summary>
/// Check if the error indicates MFA is required
/// </summary>
private static bool IsMfaRequiredError(string? error, string? errorDescription, List<string> errorCodes)
{
    // Common MFA-related error codes and descriptions
    var mfaErrorCodes = new[] { "50076", "50079", "50074", "50158" };
    var mfaKeywords = new[] { "mfa", "multi-factor", "multifactor", "strong authentication", "additional authentication" };

    // Check error codes
    if (errorCodes.Any(code => mfaErrorCodes.Contains(code)))
    {
        return true;
    }

    // Check error description for MFA keywords
    if (!string.IsNullOrEmpty(errorDescription))
    {
        var lowerDescription = errorDescription.ToLower();
        if (mfaKeywords.Any(keyword => lowerDescription.Contains(keyword)))
        {
            return true;
        }
    }

    // Check main error for MFA indicators
    if (!string.IsNullOrEmpty(error))
    {
        var lowerError = error.ToLower();
        if (mfaKeywords.Any(keyword => lowerError.Contains(keyword)))
        {
            return true;
        }
    }

    return false;
}

/// <summary>
/// Check if the error indicates Conditional Access policy requirements
/// </summary>
private static bool IsConditionalAccessError(string? error, string? errorDescription, List<string> errorCodes)
{
    var caErrorCodes = new[] { "53003", "53000", "53001", "53002", "53004" };
    var caKeywords = new[] { "conditional access", "access policy", "compliance", "device" };

    return errorCodes.Any(code => caErrorCodes.Contains(code)) ||
           (!string.IsNullOrEmpty(errorDescription) && caKeywords.Any(keyword => errorDescription.ToLower().Contains(keyword)));
}

/// <summary>
/// Check if the error indicates account is disabled or locked
/// </summary>
private static bool IsAccountDisabledError(string? error, string? errorDescription, List<string> errorCodes)
{
    var disabledErrorCodes = new[] { "50057", "50058", "50034", "50053" };
    var disabledKeywords = new[] { "disabled", "locked", "blocked", "suspended" };

    return errorCodes.Any(code => disabledErrorCodes.Contains(code)) ||
           (!string.IsNullOrEmpty(errorDescription) && disabledKeywords.Any(keyword => errorDescription.ToLower().Contains(keyword)));
}

/// <summary>
/// Check if the error indicates password has expired
/// </summary>
private static bool IsPasswordExpiredError(string? error, string? errorDescription, List<string> errorCodes)
{
    var expiredErrorCodes = new[] { "50055", "50056", "50173" };
    var expiredKeywords = new[] { "password expired", "password has expired", "change password" };

    return errorCodes.Any(code => expiredErrorCodes.Contains(code)) ||
           (!string.IsNullOrEmpty(errorDescription) && expiredKeywords.Any(keyword => errorDescription.ToLower().Contains(keyword)));
}
```

### 3. Authentication Requirements Check Endpoint

```csharp
/// <summary>
/// Check if a user account has MFA or other authentication requirements
/// </summary>
[HttpPost("check-auth-requirements")]
public async Task<IActionResult> CheckAuthRequirements([FromBody] CheckAuthRequirementsRequest request)
{
    try
    {
        _logger.LogInformation("Checking authentication requirements for {EmailOrEmployeeId}", request.EmailOrEmployeeId);

        // Find user in database first
        User? user = null;
        if (request.EmailOrEmployeeId.Contains("@"))
        {
            user = await _userService.GetUserByEmailAsync(request.EmailOrEmployeeId);
        }
        else
        {
            user = await _userService.GetUserByEmployeeIdAsync(request.EmailOrEmployeeId);
        }

        if (user == null)
        {
            return NotFound(new { error = "User not found in local database" });
        }

        if (!user.IsActive)
        {
            return BadRequest(new { error = "User account is inactive" });
        }

        if (user.IsLockedOut)
        {
            return BadRequest(new { error = "User account is locked" });
        }

        // Try a test authentication with a dummy password to detect MFA requirements
        // This will fail but give us information about authentication requirements
        var testResult = await _azureAdService.AuthenticateUserAsync(user.Email, "dummy_password_for_mfa_check");
        
        var response = new
        {
            email = user.Email,
            employeeId = user.EmployeeId,
            fullName = user.FullName,
            authenticationMethod = DetermineAuthMethod(testResult),
            requiresMfa = testResult.ErrorCode == "MFA_REQUIRED",
            requiresConditionalAccess = testResult.ErrorCode == "CONDITIONAL_ACCESS_REQUIRED",
            supportsPasswordAuth = testResult.ErrorCode != "MFA_REQUIRED" && testResult.ErrorCode != "CONDITIONAL_ACCESS_REQUIRED",
            recommendedFlow = GetRecommendedAuthFlow(testResult),
            message = GetAuthRequirementMessage(testResult, user.Email)
        };

        return Ok(response);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error checking authentication requirements for {EmailOrEmployeeId}", request.EmailOrEmployeeId);
        return StatusCode(500, new { error = "An error occurred while checking authentication requirements" });
    }
}
```

### 4. Database Integration

#### UserService.cs - Enhanced Login Logging

```csharp
public async Task LogLoginAttemptAsync(Guid? userId, string? email, string? employeeId, string ipAddress, bool success, string? failureReason = null)
{
    var loginHistory = new LoginHistory
    {
        UserId = userId,
        AttemptedEmail = email,
        AttemptedEmployeeId = employeeId,
        LoginTime = DateTime.UtcNow,
        IPAddress = ipAddress,
        Success = success,
        FailureReason = failureReason
    };

    _context.LoginHistories.Add(loginHistory);
    await _context.SaveChangesAsync();

    // If failed login, increment failed attempts
    if (!success && userId.HasValue)
    {
        var user = await _context.Users.FindAsync(userId.Value);
        if (user != null)
        {
            user.FailedAttempts++;
            
            // Lock user after 5 failed attempts
            if (user.FailedAttempts >= 5)
            {
                user.LockoutEnd = DateTime.UtcNow.AddMinutes(30);
            }
            
            await _context.SaveChangesAsync();
        }
    }
}
```

## Data Models

### AzureAdAuthResult

```csharp
public class AzureAdAuthResult
{
    public bool IsSuccess { get; set; }
    public AzureAdUser? User { get; set; }
    public string? AccessToken { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
}
```

### CheckAuthRequirementsRequest

```csharp
public class CheckAuthRequirementsRequest
{
    [Required]
    public string EmailOrEmployeeId { get; set; } = string.Empty;
}
```

## Configuration and Deployment

### Required NuGet Packages

```xml
<PackageReference Include="Microsoft.Graph" Version="5.88.0" />
<PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.7" />
<PackageReference Include="Microsoft.Identity.Client" Version="4.73.1" />
<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.6.1" />
```

### Environment Variables

```bash
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-client-id
AZURE_CLIENT_SECRET=your-client-secret
```

## Testing and Validation

### Unit Test Examples

```csharp
[Test]
public async Task Login_WithMfaEnabledUser_ReturnsMfaRequiredError()
{
    // Arrange
    var mockAzureAdService = new Mock<IAzureAdService>();
    mockAzureAdService.Setup(x => x.AuthenticateUserAsync(It.IsAny<string>(), It.IsAny<string>()))
        .ReturnsAsync(new AzureAdAuthResult 
        { 
            IsSuccess = false, 
            ErrorCode = "MFA_REQUIRED",
            ErrorMessage = "MFA is required"
        });

    var controller = new AuthController(/* dependencies */);

    // Act
    var result = await controller.Login(new UserLoginRequest 
    { 
        EmailOrEmployeeId = "<EMAIL>", 
        Password = "password123" 
    });

    // Assert
    var unauthorizedResult = result as UnauthorizedObjectResult;
    Assert.IsNotNull(unauthorizedResult);
    
    var response = unauthorizedResult.Value as dynamic;
    Assert.AreEqual("MFA_REQUIRED", response.errorCode);
    Assert.IsTrue(response.requiresOAuth);
}
```

### Integration Test Examples

```csharp
[Test]
public async Task CheckAuthRequirements_WithMfaUser_ReturnsCorrectRequirements()
{
    // Arrange
    var client = _factory.CreateClient();
    var request = new CheckAuthRequirementsRequest 
    { 
        EmailOrEmployeeId = "<EMAIL>" 
    };

    // Act
    var response = await client.PostAsJsonAsync("/api/auth/check-auth-requirements", request);

    // Assert
    response.EnsureSuccessStatusCode();
    var content = await response.Content.ReadAsStringAsync();
    var result = JsonSerializer.Deserialize<dynamic>(content);
    
    Assert.IsTrue(result.requiresMfa);
    Assert.AreEqual("oauth2", result.recommendedFlow);
}
```

## Performance Considerations

### Caching Strategy

```csharp
// Cache authentication requirements for 5 minutes
[ResponseCache(Duration = 300, VaryByQueryKeys = new[] { "emailOrEmployeeId" })]
public async Task<IActionResult> CheckAuthRequirements([FromBody] CheckAuthRequirementsRequest request)
{
    // Implementation
}
```

### Async/Await Best Practices

- All Azure AD calls are properly awaited
- Database operations use async methods
- HTTP client operations are async
- Proper exception handling with try-catch blocks

### Resource Management

```csharp
// Proper HttpClient usage with using statement
using var httpClient = new HttpClient();
httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
```

## Security Considerations

### Input Validation

- All user inputs are validated
- SQL injection prevention through parameterized queries
- XSS prevention through proper encoding

### Error Information Disclosure

- Sensitive information is not exposed in error messages
- Generic messages for security-sensitive scenarios
- Detailed logging for administrative review

### Rate Limiting

- Account lockout after failed attempts
- IP-based rate limiting (can be implemented)
- Monitoring for brute force attacks

This technical implementation provides a robust, secure, and user-friendly authentication system with comprehensive MFA detection and error handling capabilities.
