# Aviation Authentication Service - Postman Collection

This Postman collection provides comprehensive API testing for the Aviation Authentication Service, including OAuth 2.0 B2B authentication, user management, and Azure AD integration.

## 📋 Collection Overview

The collection includes the following endpoint categories:

### 🔐 OAuth 2.0 Endpoints
- **Discovery Document**: Get OAuth server configuration
- **Token Generation**: Client credentials flow for B2B authentication
- **Token Introspection**: Validate and inspect tokens
- **Token Revocation**: Revoke access tokens
- **JWKS**: JSON Web Key Set for token verification

### 👤 User Authentication
- **User Login**: Azure AD token or credential-based login
- **Token Validation**: Validate user tokens
- **Token Refresh**: Refresh expired user tokens
- **User Logout**: Revoke user sessions
- **User Profile**: Get current user information

### 🏢 Client Management
- **Client CRUD**: Create, read, update, deactivate clients
- **Client Secrets**: Regenerate client secrets
- **Client Listing**: Paginated client retrieval

### 👥 User Management
- **User CRUD**: Complete user lifecycle management
- **Role Assignment**: Assign/remove roles from users
- **User Activation**: Activate/deactivate user accounts
- **Azure AD Sync**: Synchronize users from Azure AD

### 🔑 Role Management
- **Role CRUD**: Create, read, update, delete roles
- **Role Listing**: Get all roles with filtering

### 🎯 Scope Management
- **Scope CRUD**: Manage OAuth scopes
- **Scope Categories**: Group scopes by category

### ☁️ Azure AD Integration Tests
- **Configuration Test**: Verify Azure AD configuration
- **Service Test**: Test Azure AD service instantiation
- **Connection Test**: Test Microsoft Graph API connectivity

## 🚀 Getting Started

### 1. Import the Collection

1. Open Postman
2. Click **Import**
3. Select the `Aviation-Authentication-Service-Postman-Collection.json` file
4. The collection will be imported with all endpoints and variables

### 2. Configure Environment Variables

Update the following variables in the collection or create a new environment:

#### Required Variables:
```
baseUrl: http://localhost:5293
client_id: your-actual-client-id
client_secret: your-actual-client-secret
user_email: <EMAIL>
user_password: your-user-password
```

#### Auto-Generated Variables:
These are automatically set by test scripts:
```
access_token: (OAuth access token)
user_token: (User authentication token)
client_db_id: (Database ID of created client)
user_id: (User GUID)
role_id: (Role GUID)
scope_id: (Scope ID)
```

### 3. Start the Authentication Service

Ensure the Aviation Authentication Service is running:
```bash
cd Authentication-service/Aviation.Authentication.Api
dotnet run
```

The service should be available at `http://localhost:5293`

## 📝 Usage Workflow

### A. OAuth 2.0 B2B Authentication Flow

1. **Create a Client** (if needed):
   - First, authenticate as a user (see User Authentication section)
   - Use `Client Management > Create New Client`
   - Note the returned `client_id` and `client_secret`

2. **Get Access Token**:
   - Use `OAuth 2.0 Endpoints > Get Access Token (Client Credentials)`
   - The `access_token` will be automatically saved to variables

3. **Use Protected Endpoints**:
   - The token is automatically used in subsequent API calls
   - Test with `OAuth 2.0 Endpoints > Token Introspection`

### B. User Authentication Flow

1. **User Login**:
   - Use `User Authentication > User Login (Credentials)`
   - Or `User Authentication > User Login (Azure AD)` if you have Azure AD tokens
   - The `user_token` will be automatically saved

2. **Access User Profile**:
   - Use `User Authentication > Get User Profile`

3. **Manage Users**:
   - Use endpoints in `User Management` section

### C. Administrative Operations

1. **Manage Clients**:
   - Use `Client Management` endpoints to create/update B2B clients

2. **Manage Users**:
   - Use `User Management` endpoints for user lifecycle

3. **Manage Roles & Scopes**:
   - Use `Role Management` and `Scope Management` sections

## 🔧 Testing Azure AD Integration

1. **Check Configuration**:
   ```
   GET /api/azureadtest/config
   ```

2. **Test Service**:
   ```
   GET /api/azureadtest/test-service
   ```

3. **Test Connection** (requires proper Azure AD setup):
   ```
   GET /api/azureadtest/test-connection
   ```

## 📊 Sample Test Data

### Sample Client Creation:
```json
{
    "clientName": "Test Partner API Client",
    "description": "Test client for partner integration",
    "clientType": "Partner",
    "scopes": ["partner:read", "partner:write"],
    "accessTokenLifetimeSeconds": 3600,
    "rateLimitPerHour": 1000
}
```

### Sample User Creation:
```json
{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "employeeId": "EMP001",
    "department": "Operations",
    "jobTitle": "Flight Coordinator",
    "phoneNumber": "******-0123",
    "isActive": true,
    "roleIds": ["role-guid-here"]
}
```

## 🛠️ Troubleshooting

### Common Issues:

1. **401 Unauthorized**:
   - Check if your token is valid and not expired
   - Ensure you're using the correct token type (user_token vs access_token)

2. **403 Forbidden**:
   - Verify your user has the required permissions
   - Check role assignments

3. **404 Not Found**:
   - Verify the service is running on the correct port
   - Check endpoint URLs

4. **Azure AD Connection Timeout**:
   - Verify Azure AD configuration in appsettings.json
   - Check if the Azure AD app registration has proper permissions

### Health Check:
Always start with the health check endpoint:
```
GET /health
```

## 📚 Additional Resources

- **Swagger Documentation**: `http://localhost:5293/swagger`
- **OAuth Discovery**: `http://localhost:5293/oauth/.well-known/oauth-authorization-server`
- **Service Health**: `http://localhost:5293/health`

## 🔒 Security Notes

- Never commit real client secrets or passwords to version control
- Use environment-specific variables for different deployment environments
- Regularly rotate client secrets in production
- Monitor token usage and implement proper rate limiting

## 📞 Support

For issues or questions about the Aviation Authentication Service API, please refer to the service documentation or contact the development team.
