using Microsoft.AspNetCore.Authorization;

namespace Aviation.Auth.OAuth2.Authorization;

/// <summary>
/// Authorization requirement for OAuth 2.0 scopes
/// </summary>
public class ScopeRequirement : IAuthorizationRequirement
{
    public string[] RequiredScopes { get; }
    public bool RequireAllScopes { get; }

    public ScopeRequirement(params string[] requiredScopes) : this(false, requiredScopes)
    {
    }

    public ScopeRequirement(bool requireAllScopes, params string[] requiredScopes)
    {
        RequiredScopes = requiredScopes ?? throw new ArgumentNullException(nameof(requiredScopes));
        RequireAllScopes = requireAllScopes;
    }
}

/// <summary>
/// Authorization handler for scope requirements
/// </summary>
public class ScopeAuthorizationHandler : AuthorizationHandler<ScopeRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, ScopeRequirement requirement)
    {
        // Get scopes from the user's claims
        var userScopes = context.User.Claims
            .Where(c => c.Type == "scope")
            .Select(c => c.Value)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        // Check if user has required scopes
        bool hasRequiredScopes;
        
        if (requirement.RequireAllScopes)
        {
            // User must have ALL required scopes
            hasRequiredScopes = requirement.RequiredScopes.All(scope => userScopes.Contains(scope));
        }
        else
        {
            // User must have at least ONE of the required scopes
            hasRequiredScopes = requirement.RequiredScopes.Any(scope => userScopes.Contains(scope));
        }

        if (hasRequiredScopes)
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// Attribute for applying scope-based authorization to controllers/actions
/// </summary>
public class RequireScopeAttribute : AuthorizeAttribute
{
    public RequireScopeAttribute(params string[] scopes)
    {
        Policy = $"RequireScope_{string.Join("_", scopes)}";
    }
}
