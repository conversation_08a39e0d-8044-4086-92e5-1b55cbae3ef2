using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace Aviation.Auth.OAuth2.Middleware;

/// <summary>
/// Middleware for handling OAuth 2.0 Client Credentials authentication
/// </summary>
public class OAuth2AuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<OAuth2AuthenticationMiddleware> _logger;

    public OAuth2AuthenticationMiddleware(RequestDelegate next, ILogger<OAuth2AuthenticationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Extract and validate the Bearer token
            var token = ExtractBearerToken(context.Request);
            
            if (!string.IsNullOrEmpty(token))
            {
                var claims = ValidateAndExtractClaims(token);
                if (claims != null)
                {
                    // Set the user principal with extracted claims
                    context.User = new ClaimsPrincipal(new ClaimsIdentity(claims, "OAuth2"));
                    
                    // Add client information to context items for logging/auditing
                    AddClientInfoToContext(context, claims);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing OAuth 2.0 token");
        }

        await _next(context);
    }

    private string? ExtractBearerToken(HttpRequest request)
    {
        var authHeader = request.Headers.Authorization.FirstOrDefault();
        
        if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
        {
            return null;
        }

        return authHeader.Substring("Bearer ".Length).Trim();
    }

    private IEnumerable<Claim>? ValidateAndExtractClaims(string token)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);
            
            // Basic validation - more comprehensive validation happens in JWT middleware
            if (jsonToken.ValidTo < DateTime.UtcNow)
            {
                _logger.LogWarning("Token has expired");
                return null;
            }

            return jsonToken.Claims;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse JWT token");
            return null;
        }
    }

    private void AddClientInfoToContext(HttpContext context, IEnumerable<Claim> claims)
    {
        var clientId = claims.FirstOrDefault(c => c.Type == "client_id")?.Value;
        var scopes = claims.Where(c => c.Type == "scope").Select(c => c.Value).ToArray();
        
        if (!string.IsNullOrEmpty(clientId))
        {
            context.Items["OAuth2.ClientId"] = clientId;
            context.Items["OAuth2.Scopes"] = scopes;
            
            _logger.LogDebug("OAuth 2.0 client authenticated: {ClientId} with scopes: {Scopes}", 
                clientId, string.Join(", ", scopes));
        }
    }
}
