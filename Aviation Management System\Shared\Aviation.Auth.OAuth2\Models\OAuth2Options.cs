namespace Aviation.Auth.OAuth2.Models;

public class OAuth2Options
{
    public const string SectionName = "OAuth2";
    
    /// <summary>
    /// The authority URL for the OAuth 2.0 server (e.g., https://your-auth-server.com)
    /// </summary>
    public string Authority { get; set; } = string.Empty;
    
    /// <summary>
    /// The audience for JWT tokens (typically your API identifier)
    /// </summary>
    public string Audience { get; set; } = string.Empty;
    
    /// <summary>
    /// The issuer of JWT tokens
    /// </summary>
    public string Issuer { get; set; } = string.Empty;
    
    /// <summary>
    /// JWT signing key or certificate path
    /// </summary>
    public string SigningKey { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether to require HTTPS for metadata discovery
    /// </summary>
    public bool RequireHttpsMetadata { get; set; } = true;
    
    /// <summary>
    /// Token validation parameters
    /// </summary>
    public TokenValidationOptions TokenValidation { get; set; } = new();
}

public class TokenValidationOptions
{
    /// <summary>
    /// Whether to validate the token lifetime
    /// </summary>
    public bool ValidateLifetime { get; set; } = true;
    
    /// <summary>
    /// Whether to validate the token issuer
    /// </summary>
    public bool ValidateIssuer { get; set; } = true;
    
    /// <summary>
    /// Whether to validate the token audience
    /// </summary>
    public bool ValidateAudience { get; set; } = true;
    
    /// <summary>
    /// Whether to validate the issuer signing key
    /// </summary>
    public bool ValidateIssuerSigningKey { get; set; } = true;
    
    /// <summary>
    /// Clock skew tolerance in minutes
    /// </summary>
    public int ClockSkewMinutes { get; set; } = 5;
}
