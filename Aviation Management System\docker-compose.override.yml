# Override file for development environment
version: '3.8'

services:
  # Development overrides for API Gateway
  api-gateway:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Logging__LogLevel__Default=Debug
      - Logging__LogLevel__Microsoft.AspNetCore=Information
    volumes:
      - ./api-gateway/Aviation.ApiGateway:/app/source
      - ./logs/api-gateway:/app/logs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-gateway.rule=Host(`api.localhost`)"
      - "traefik.http.services.api-gateway.loadbalancer.server.port=80"

  # Development overrides for Partner Customer Service
  partner-customer-service:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Logging__LogLevel__Default=Debug
    volumes:
      - ./partner-customer-service/Aviation.PartnerCustomer.Api:/app/source
      - ./logs/partner-customer:/app/logs

  # Development overrides for Order Management Service
  order-management-service:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Logging__LogLevel__Default=Debug
    volumes:
      - ./order-management-service/Aviation.OrderManagement.Api:/app/source
      - ./logs/order-management:/app/logs

  # Development overrides for Finance Billing Service
  finance-billing-service:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Logging__LogLevel__Default=Debug
    volumes:
      - ./finance-billing-service/Aviation.FinanceBilling.Api:/app/source
      - ./logs/finance-billing:/app/logs

  # Development overrides for Product Pricing Service
  product-pricing-service:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Logging__LogLevel__Default=Debug
    volumes:
      - ./product-pricing-service/Aviation.ProductPricing.Api:/app/source
      - ./logs/product-pricing:/app/logs

  # Development overrides for Trip Estimation Service
  trip-estimation-service:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Logging__LogLevel__Default=Debug
    volumes:
      - ./trip-estimation-service/Aviation.TripEstimation.Api:/app/source
      - ./logs/trip-estimation:/app/logs

  # Development overrides for Document Service
  document-service:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Logging__LogLevel__Default=Debug
    volumes:
      - ./document-service/Aviation.Document.Api:/app/source
      - ./logs/document:/app/logs

  # Development overrides for App Registry Service
  app-registry-service:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Logging__LogLevel__Default=Debug
    volumes:
      - ./devportal-appregistry-service/Aviation.AppRegistry.Api:/app/source
      - ./logs/app-registry:/app/logs

  # SQL Server with development settings
  sqlserver:
    environment:
      - MSSQL_COLLATION=SQL_Latin1_General_CP1_CI_AS
    volumes:
      - ./scripts/sql:/docker-entrypoint-initdb.d
      - ./data/sqlserver:/var/opt/mssql/data
    ports:
      - "1433:1433"

  # Redis for caching (development)
  redis:
    image: redis:7-alpine
    container_name: aviation-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aviation-network
    command: redis-server --appendonly yes

  # Seq for centralized logging (development)
  seq:
    image: datalust/seq:latest
    container_name: aviation-seq
    environment:
      - ACCEPT_EULA=Y
    ports:
      - "5341:80"
    volumes:
      - seq_data:/data
    networks:
      - aviation-network

  # Jaeger for distributed tracing (development)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: aviation-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - aviation-network

  # Prometheus for metrics (development)
  prometheus:
    image: prom/prometheus:latest
    container_name: aviation-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - aviation-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana for dashboards (development)
  grafana:
    image: grafana/grafana:latest
    container_name: aviation-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - aviation-network

volumes:
  redis_data:
  seq_data:
  prometheus_data:
  grafana_data:
