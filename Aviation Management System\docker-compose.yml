version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: aviation-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong!Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong!Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Authentication Service
  authentication-service:
    build:
      context: ./Authentication-service/Aviation.Authentication.Api
      dockerfile: Dockerfile
    container_name: aviation-authentication
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=AviationAuthentication;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;
      - JWT__SecretKey=dev-aviation-auth-secret-key-256-bits-minimum-length-required
      - JWT__Issuer=http://localhost:8080
      - JWT__Audience=aviation-api-dev
    ports:
      - "8080:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway/Aviation.ApiGateway
      dockerfile: Dockerfile
    container_name: aviation-api-gateway
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - OAuth2__Authority=http://authentication-service:80
      - OAuth2__Audience=aviation-api-dev
      - OAuth2__Issuer=http://localhost:8080
      - OAuth2__SigningKey=dev-aviation-auth-secret-key-256-bits-minimum-length-required
      - OAuth2__RequireHttpsMetadata=false
    ports:
      - "5000:80"
    depends_on:
      - authentication-service
      - partner-customer-service
      - order-management-service
      - finance-billing-service
      - product-pricing-service
      - trip-estimation-service
      - document-service
      - app-registry-service
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Partner & Customer Service
  partner-customer-service:
    build:
      context: ./partner-customer-service/Aviation.PartnerCustomer.Api
      dockerfile: Dockerfile
    container_name: aviation-partner-customer
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=AviationPartnerCustomer;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;
      - OAuth2__Authority=http://authentication-service:80
      - OAuth2__Audience=aviation-api-dev
      - OAuth2__Issuer=http://localhost:8080
      - OAuth2__SigningKey=dev-aviation-auth-secret-key-256-bits-minimum-length-required
      - OAuth2__RequireHttpsMetadata=false
    ports:
      - "5001:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Order Management Service
  order-management-service:
    build:
      context: ./order-management-service/Aviation.OrderManagement.Api
      dockerfile: Dockerfile
    container_name: aviation-order-management
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=AviationOrderManagement;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;
      - OAuth2__Authority=http://localhost:8080
      - OAuth2__Audience=aviation-api
      - OAuth2__SigningKey=dev-signing-key-256-bits-minimum-length-required
      - OAuth2__RequireHttpsMetadata=false
    ports:
      - "5002:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Finance Billing Service
  finance-billing-service:
    build:
      context: ./finance-billing-service/Aviation.FinanceBilling.Api
      dockerfile: Dockerfile
    container_name: aviation-finance-billing
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=AviationFinanceBilling;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;
      - OAuth2__Authority=http://localhost:8080
      - OAuth2__Audience=aviation-api
      - OAuth2__SigningKey=dev-signing-key-256-bits-minimum-length-required
      - OAuth2__RequireHttpsMetadata=false
    ports:
      - "5003:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Product Pricing Service
  product-pricing-service:
    build:
      context: ./product-pricing-service/Aviation.ProductPricing.Api
      dockerfile: Dockerfile
    container_name: aviation-product-pricing
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=AviationProductPricing;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;
      - OAuth2__Authority=http://localhost:8080
      - OAuth2__Audience=aviation-api
      - OAuth2__SigningKey=dev-signing-key-256-bits-minimum-length-required
      - OAuth2__RequireHttpsMetadata=false
    ports:
      - "5004:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Trip Estimation Service
  trip-estimation-service:
    build:
      context: ./trip-estimation-service/Aviation.TripEstimation.Api
      dockerfile: Dockerfile
    container_name: aviation-trip-estimation
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=AviationTripEstimation;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;
      - OAuth2__Authority=http://localhost:8080
      - OAuth2__Audience=aviation-api
      - OAuth2__SigningKey=dev-signing-key-256-bits-minimum-length-required
      - OAuth2__RequireHttpsMetadata=false
    ports:
      - "5005:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Document Service
  document-service:
    build:
      context: ./document-service/Aviation.Document.Api
      dockerfile: Dockerfile
    container_name: aviation-document
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=AviationDocument;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;
      - OAuth2__Authority=http://localhost:8080
      - OAuth2__Audience=aviation-api
      - OAuth2__SigningKey=dev-signing-key-256-bits-minimum-length-required
      - OAuth2__RequireHttpsMetadata=false
    ports:
      - "5006:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Developer App Registry Service
  app-registry-service:
    build:
      context: ./devportal-appregistry-service/Aviation.AppRegistry.Api
      dockerfile: Dockerfile
    container_name: aviation-app-registry
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=AviationAppRegistry;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;
      - OAuth2__Authority=http://localhost:8080
      - OAuth2__Audience=aviation-api
      - OAuth2__SigningKey=dev-signing-key-256-bits-minimum-length-required
      - OAuth2__RequireHttpsMetadata=false
    ports:
      - "5007:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - aviation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  sqlserver_data:
    driver: local

networks:
  aviation-network:
    driver: bridge
