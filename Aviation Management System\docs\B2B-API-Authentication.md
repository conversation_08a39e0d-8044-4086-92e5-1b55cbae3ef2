# Aviation Management System - B2B API Authentication

## Overview

The Aviation Management System provides B2B API access through OAuth 2.0 Client Credentials flow. This document outlines the authentication process, available scopes, and integration guidelines for B2B partners.

## Authentication Flow

### OAuth 2.0 Client Credentials

The system uses OAuth 2.0 Client Credentials grant type for machine-to-machine authentication. This flow is ideal for B2B integrations where there's no user interaction required.

#### Flow Steps:
1. Partner obtains client credentials (client_id and client_secret)
2. Partner requests access token from authorization server
3. Partner uses access token to make API calls
4. Token expires and partner requests new token

### Token Endpoint

```
POST https://your-auth-server.com/oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
&client_id=YOUR_CLIENT_ID
&client_secret=YOUR_CLIENT_SECRET
&scope=partner:read order:read order:write
```

### Response

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "partner:read order:read order:write"
}
```

## Available B2B Services and Scopes

### 1. Partner & Customer Service
- **Base URL**: `https://api.aviation-management.com/api/v1/partners`
- **Scopes**:
  - `partner:read` - Read partner information
  - `partner:write` - Create/update partner information
  - `customer:read` - Read customer information
  - `customer:write` - Create/update customer information

### 2. Order Management Service
- **Base URL**: `https://api.aviation-management.com/api/v1/orders`
- **Scopes**:
  - `order:read` - Read order information
  - `order:write` - Create/update orders
  - `order:cancel` - Cancel orders

### 3. Invoice/Finance Billing Service
- **Base URL**: `https://api.aviation-management.com/api/v1/invoices`
- **Scopes**:
  - `invoice:read` - Read invoice information
  - `invoice:write` - Create/update invoices
  - `billing:read` - Read billing information

### 4. Product & Pricing Service
- **Base URL**: `https://api.aviation-management.com/api/v1/products`
- **Scopes**:
  - `product:read` - Read product catalog
  - `product:write` - Update product information
  - `pricing:read` - Read pricing information
  - `pricing:write` - Update pricing information

### 5. Trip Estimation Service
- **Base URL**: `https://api.aviation-management.com/api/v1/trip-estimation`
- **Scopes**:
  - `trip:estimate` - Get trip cost estimates
  - `flight:read` - Read flight information
  - `airport:read` - Read airport information

### 6. Document Service (Optional)
- **Base URL**: `https://api.aviation-management.com/api/v1/documents`
- **Scopes**:
  - `document:read` - Read documents
  - `document:write` - Upload/update documents
  - `document:delete` - Delete documents

### 7. Developer App Registry Service
- **Base URL**: `https://api.aviation-management.com/api/v1/app-registry`
- **Scopes**:
  - `app:read` - Read application information
  - `app:write` - Register/update applications
  - `app:admin` - Administrative access to app registry

## Making API Calls

### Authorization Header

Include the access token in the Authorization header:

```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Example API Call

```bash
curl -X GET \
  https://api.aviation-management.com/api/v1/partners \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json'
```

## Error Handling

### Authentication Errors

- **401 Unauthorized**: Invalid or expired token
- **403 Forbidden**: Insufficient scopes for the requested resource

### Example Error Response

```json
{
  "error": "insufficient_scope",
  "error_description": "The request requires higher privileges than provided by the access token.",
  "required_scopes": ["order:write"]
}
```

## Rate Limiting

- **Rate Limit**: 1000 requests per hour per client
- **Headers**: 
  - `X-RateLimit-Limit`: Maximum requests per hour
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when rate limit resets

## Security Best Practices

1. **Store credentials securely**: Never expose client_secret in client-side code
2. **Use HTTPS**: All API calls must use HTTPS
3. **Token management**: Implement proper token caching and refresh logic
4. **Scope principle**: Request only the minimum required scopes
5. **Monitor usage**: Track API usage and implement proper logging

## Client Onboarding Process

1. **Application**: Submit B2B partnership application
2. **Review**: Technical and business review process
3. **Credentials**: Receive client_id and client_secret
4. **Testing**: Access to sandbox environment for testing
5. **Production**: Access to production environment after testing approval

## Support and Contact

- **Technical Support**: <EMAIL>
- **Business Inquiries**: <EMAIL>
- **Documentation**: https://docs.aviation-management.com
- **Status Page**: https://status.aviation-management.com
