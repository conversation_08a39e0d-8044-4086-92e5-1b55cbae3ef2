apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: aviation-system
  labels:
    app: api-gateway
    version: v1
    component: gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        version: v1
    spec:
      containers:
      - name: api-gateway
        image: aviation/api-gateway:latest
        ports:
        - containerPort: 80
          name: http
        - containerPort: 443
          name: https
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ASPNETCORE_URLS
          value: "http://+:80"
        - name: OAuth2__Authority
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: authority
        - name: OAuth2__Audience
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: audience
        - name: OAuth2__Issuer
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: issuer
        - name: OAuth2__SigningKey
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: signing-key
        - name: ServiceDiscovery__PartnerCustomerService
          valueFrom:
            configMapKeyRef:
              name: service-discovery-config
              key: partner-customer-service-url
        - name: ServiceDiscovery__OrderManagementService
          valueFrom:
            configMapKeyRef:
              name: service-discovery-config
              key: order-management-service-url
        - name: ServiceDiscovery__FinanceBillingService
          valueFrom:
            configMapKeyRef:
              name: service-discovery-config
              key: finance-billing-service-url
        - name: ServiceDiscovery__ProductPricingService
          valueFrom:
            configMapKeyRef:
              name: service-discovery-config
              key: product-pricing-service-url
        - name: ServiceDiscovery__TripEstimationService
          valueFrom:
            configMapKeyRef:
              name: service-discovery-config
              key: trip-estimation-service-url
        - name: ServiceDiscovery__DocumentService
          valueFrom:
            configMapKeyRef:
              name: service-discovery-config
              key: document-service-url
        - name: ServiceDiscovery__AppRegistryService
          valueFrom:
            configMapKeyRef:
              name: service-discovery-config
              key: app-registry-service-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
      securityContext:
        fsGroup: 2000
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
  namespace: aviation-system
  labels:
    app: api-gateway
spec:
  selector:
    app: api-gateway
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: https
    port: 443
    targetPort: 443
    protocol: TCP
  type: LoadBalancer
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-gateway-ingress
  namespace: aviation-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.aviation-management.com
    secretName: aviation-api-tls
  rules:
  - host: api.aviation-management.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 80
