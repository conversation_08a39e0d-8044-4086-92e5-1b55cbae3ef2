# Partner & Customer Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: partner-customer-api
  namespace: aviation-system
  labels:
    app: partner-customer-api
    version: v1
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: partner-customer-api
  template:
    metadata:
      labels:
        app: partner-customer-api
        version: v1
    spec:
      containers:
      - name: partner-customer-api
        image: aviation/partner-customer-api:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          value: "Server=$(DATABASE_HOST);Database=AviationPartnerCustomer;User Id=$(DATABASE_USERNAME);Password=$(DATABASE_PASSWORD);TrustServerCertificate=true;"
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: database-config
              key: sql-server-host
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-password
        - name: OAuth2__Authority
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: authority
        - name: OAuth2__Audience
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: audience
        - name: OAuth2__SigningKey
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: signing-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: partner-customer-service
  namespace: aviation-system
  labels:
    app: partner-customer-api
spec:
  selector:
    app: partner-customer-api
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
---
# Finance Billing Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: finance-billing-api
  namespace: aviation-system
  labels:
    app: finance-billing-api
    version: v1
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: finance-billing-api
  template:
    metadata:
      labels:
        app: finance-billing-api
        version: v1
    spec:
      containers:
      - name: finance-billing-api
        image: aviation/finance-billing-api:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          value: "Server=$(DATABASE_HOST);Database=AviationFinanceBilling;User Id=$(DATABASE_USERNAME);Password=$(DATABASE_PASSWORD);TrustServerCertificate=true;"
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: database-config
              key: sql-server-host
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-password
        - name: OAuth2__Authority
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: authority
        - name: OAuth2__Audience
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: audience
        - name: OAuth2__SigningKey
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: signing-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: finance-billing-service
  namespace: aviation-system
  labels:
    app: finance-billing-api
spec:
  selector:
    app: finance-billing-api
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
---
# Product Pricing Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: product-pricing-api
  namespace: aviation-system
  labels:
    app: product-pricing-api
    version: v1
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: product-pricing-api
  template:
    metadata:
      labels:
        app: product-pricing-api
        version: v1
    spec:
      containers:
      - name: product-pricing-api
        image: aviation/product-pricing-api:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          value: "Server=$(DATABASE_HOST);Database=AviationProductPricing;User Id=$(DATABASE_USERNAME);Password=$(DATABASE_PASSWORD);TrustServerCertificate=true;"
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: database-config
              key: sql-server-host
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-password
        - name: OAuth2__Authority
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: authority
        - name: OAuth2__Audience
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: audience
        - name: OAuth2__SigningKey
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: signing-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: product-pricing-service
  namespace: aviation-system
  labels:
    app: product-pricing-api
spec:
  selector:
    app: product-pricing-api
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
---
# Trip Estimation Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trip-estimation-api
  namespace: aviation-system
  labels:
    app: trip-estimation-api
    version: v1
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: trip-estimation-api
  template:
    metadata:
      labels:
        app: trip-estimation-api
        version: v1
    spec:
      containers:
      - name: trip-estimation-api
        image: aviation/trip-estimation-api:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          value: "Server=$(DATABASE_HOST);Database=AviationTripEstimation;User Id=$(DATABASE_USERNAME);Password=$(DATABASE_PASSWORD);TrustServerCertificate=true;"
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: database-config
              key: sql-server-host
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-password
        - name: OAuth2__Authority
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: authority
        - name: OAuth2__Audience
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: audience
        - name: OAuth2__SigningKey
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: signing-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: trip-estimation-service
  namespace: aviation-system
  labels:
    app: trip-estimation-api
spec:
  selector:
    app: trip-estimation-api
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
---
# Document Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: document-api
  namespace: aviation-system
  labels:
    app: document-api
    version: v1
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: document-api
  template:
    metadata:
      labels:
        app: document-api
        version: v1
    spec:
      containers:
      - name: document-api
        image: aviation/document-api:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          value: "Server=$(DATABASE_HOST);Database=AviationDocument;User Id=$(DATABASE_USERNAME);Password=$(DATABASE_PASSWORD);TrustServerCertificate=true;"
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: database-config
              key: sql-server-host
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-password
        - name: OAuth2__Authority
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: authority
        - name: OAuth2__Audience
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: audience
        - name: OAuth2__SigningKey
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: signing-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: document-service
  namespace: aviation-system
  labels:
    app: document-api
spec:
  selector:
    app: document-api
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
---
# Developer App Registry Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-registry-api
  namespace: aviation-system
  labels:
    app: app-registry-api
    version: v1
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: app-registry-api
  template:
    metadata:
      labels:
        app: app-registry-api
        version: v1
    spec:
      containers:
      - name: app-registry-api
        image: aviation/app-registry-api:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          value: "Server=$(DATABASE_HOST);Database=AviationAppRegistry;User Id=$(DATABASE_USERNAME);Password=$(DATABASE_PASSWORD);TrustServerCertificate=true;"
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: database-config
              key: sql-server-host
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-password
        - name: OAuth2__Authority
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: authority
        - name: OAuth2__Audience
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: audience
        - name: OAuth2__SigningKey
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: signing-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: app-registry-service
  namespace: aviation-system
  labels:
    app: app-registry-api
spec:
  selector:
    app: app-registry-api
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
