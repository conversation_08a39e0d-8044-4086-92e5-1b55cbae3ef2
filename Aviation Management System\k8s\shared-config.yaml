apiVersion: v1
kind: ConfigMap
metadata:
  name: oauth2-config
  namespace: aviation-system
  labels:
    app: aviation-shared
    component: config
data:
  authority: "https://auth.aviation-management.com"
  audience: "aviation-api"
  issuer: "https://auth.aviation-management.com"
  require-https-metadata: "true"
  discovery-endpoint: "https://auth.aviation-management.com/oauth/.well-known/oauth-authorization-server"
  token-endpoint: "https://auth.aviation-management.com/oauth/token"
  introspection-endpoint: "https://auth.aviation-management.com/oauth/introspect"
---
apiVersion: v1
kind: Secret
metadata:
  name: oauth2-secrets
  namespace: aviation-system
  labels:
    app: aviation-shared
    component: secrets
type: Opaque
stringData:
  signing-key: "aviation-auth-super-secret-key-256-bits-minimum-length-required-for-production"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: database-config
  namespace: aviation-system
  labels:
    app: aviation-shared
    component: config
data:
  sql-server-host: "sql-server-service"
  sql-server-port: "1433"
---
apiVersion: v1
kind: Secret
metadata:
  name: database-secrets
  namespace: aviation-system
  labels:
    app: aviation-shared
    component: secrets
type: Opaque
stringData:
  sql-server-username: "sa"
  sql-server-password: "YourStrong!Passw0rd"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: logging-config
  namespace: aviation-system
  labels:
    app: aviation-shared
    component: config
data:
  log-level: "Information"
  aspnetcore-log-level: "Warning"
  ef-log-level: "Warning"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: service-discovery-config
  namespace: aviation-system
  labels:
    app: aviation-shared
    component: config
data:
  api-gateway-url: "http://api-gateway-service"
  partner-customer-service-url: "http://partner-customer-service"
  order-management-service-url: "http://order-management-service"
  finance-billing-service-url: "http://finance-billing-service"
  product-pricing-service-url: "http://product-pricing-service"
  trip-estimation-service-url: "http://trip-estimation-service"
  document-service-url: "http://document-service"
  app-registry-service-url: "http://app-registry-service"
