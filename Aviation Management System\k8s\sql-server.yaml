apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: sql-server
  namespace: aviation-system
  labels:
    app: sql-server
    component: database
spec:
  serviceName: sql-server-service
  replicas: 1
  selector:
    matchLabels:
      app: sql-server
  template:
    metadata:
      labels:
        app: sql-server
    spec:
      containers:
      - name: sql-server
        image: mcr.microsoft.com/mssql/server:2022-latest
        ports:
        - containerPort: 1433
          name: sql
        env:
        - name: ACCEPT_EULA
          value: "Y"
        - name: SA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: sql-server-password
        - name: MSSQL_PID
          value: "Express"
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1000m"
        volumeMounts:
        - name: sql-data
          mountPath: /var/opt/mssql
        livenessProbe:
          tcpSocket:
            port: 1433
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          tcpSocket:
            port: 1433
          initialDelaySeconds: 30
          periodSeconds: 10
  volumeClaimTemplates:
  - metadata:
      name: sql-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: sql-server-service
  namespace: aviation-system
  labels:
    app: sql-server
spec:
  selector:
    app: sql-server
  ports:
  - name: sql
    port: 1433
    targetPort: 1433
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: sql-server-backup-pvc
  namespace: aviation-system
  labels:
    app: sql-server
    component: backup
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
