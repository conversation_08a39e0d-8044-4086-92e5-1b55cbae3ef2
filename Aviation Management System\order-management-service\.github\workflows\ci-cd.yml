name: Order Management Service CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'order-management-service/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'order-management-service/**'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: aviation/order-management-api

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore order-management-service/Aviation.OrderManagement.Api/Aviation.OrderManagement.Api.csproj
    
    - name: Build
      run: dotnet build order-management-service/Aviation.OrderManagement.Api/Aviation.OrderManagement.Api.csproj --no-restore
    
    - name: Test
      run: dotnet test order-management-service/Aviation.OrderManagement.Api.Tests/Aviation.OrderManagement.Api.Tests.csproj --no-build --verbosity normal --collect:"XPlat Code Coverage"
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: order-management-service

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./order-management-service
        file: ./order-management-service/Aviation.OrderManagement.Api/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}
    
    - name: Deploy to staging
      run: |
        kubectl apply -f order-management-service/k8s/configmap.yaml
        kubectl apply -f order-management-service/k8s/deployment.yaml
        kubectl rollout status deployment/order-management-api -n aviation-system --timeout=300s

  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}
    
    - name: Deploy to production
      run: |
        kubectl apply -f order-management-service/k8s/configmap.yaml
        kubectl apply -f order-management-service/k8s/deployment.yaml
        kubectl rollout status deployment/order-management-api -n aviation-system --timeout=300s
    
    - name: Run smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app=order-management-api -n aviation-system --timeout=300s
        # Add smoke test commands here
        echo "Smoke tests passed"
