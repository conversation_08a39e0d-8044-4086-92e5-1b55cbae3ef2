# Use the official .NET 8 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 8 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Aviation.OrderManagement.Api/Aviation.OrderManagement.Api.csproj", "Aviation.OrderManagement.Api/"]
COPY ["../../Shared/Aviation.Auth.OAuth2/Aviation.Auth.OAuth2.csproj", "Shared/Aviation.Auth.OAuth2/"]

# Restore dependencies
RUN dotnet restore "Aviation.OrderManagement.Api/Aviation.OrderManagement.Api.csproj"

# Copy source code
COPY . .
WORKDIR "/src/Aviation.OrderManagement.Api"

# Build the application
RUN dotnet build "Aviation.OrderManagement.Api.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "Aviation.OrderManagement.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

ENTRYPOINT ["dotnet", "Aviation.OrderManagement.Api.dll"]
