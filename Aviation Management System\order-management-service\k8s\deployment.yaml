apiVersion: apps/v1
kind: Deployment
metadata:
  name: order-management-api
  namespace: aviation-system
  labels:
    app: order-management-api
    version: v1
    component: microservice
spec:
  replicas: 3
  selector:
    matchLabels:
      app: order-management-api
  template:
    metadata:
      labels:
        app: order-management-api
        version: v1
    spec:
      containers:
      - name: order-management-api
        image: aviation/order-management-api:latest
        ports:
        - containerPort: 80
          name: http
        - containerPort: 443
          name: https
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ASPNETCORE_URLS
          value: "http://+:80"
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: order-management-secrets
              key: database-connection-string
        - name: OAuth2__Authority
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: authority
        - name: OAuth2__Audience
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: audience
        - name: OAuth2__Issuer
          valueFrom:
            configMapKeyRef:
              name: oauth2-config
              key: issuer
        - name: OAuth2__SigningKey
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: signing-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
      securityContext:
        fsGroup: 2000
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: order-management-service
  namespace: aviation-system
  labels:
    app: order-management-api
spec:
  selector:
    app: order-management-api
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: https
    port: 443
    targetPort: 443
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: order-management-ingress
  namespace: aviation-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.aviation-management.com
    secretName: aviation-api-tls
  rules:
  - host: api.aviation-management.com
    http:
      paths:
      - path: /api/v1/orders(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: order-management-service
            port:
              number: 80
