using Aviation.Auth.OAuth2.Authorization;
using Aviation.Auth.OAuth2.Models;
using Aviation.PartnerCustomer.Api.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Aviation.PartnerCustomer.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CustomersController : ControllerBase
{
    private readonly ILogger<CustomersController> _logger;

    public CustomersController(ILogger<CustomersController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get all customers
    /// </summary>
    [HttpGet]
    [RequireScope(B2BScopes.CustomerRead)]
    public async Task<ActionResult<IEnumerable<Customer>>> GetCustomers([FromQuery] int? partnerId = null)
    {
        _logger.LogInformation("Getting customers for partner: {PartnerId}", partnerId);
        
        // Mock data for demonstration
        var customers = new List<Customer>
        {
            new Customer
            {
                Id = 1,
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Phone = "******-1234",
                PassportNumber = "US123456789",
                Nationality = "USA",
                DateOfBirth = new DateTime(1985, 5, 15),
                Status = CustomerStatus.Active,
                PartnerId = 1,
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                UpdatedAt = DateTime.UtcNow.AddDays(-2)
            },
            new Customer
            {
                Id = 2,
                FirstName = "Jane",
                LastName = "Smith",
                Email = "<EMAIL>",
                Phone = "******-5678",
                PassportNumber = "US987654321",
                Nationality = "USA",
                DateOfBirth = new DateTime(1990, 8, 22),
                Status = CustomerStatus.Active,
                PartnerId = 2,
                CreatedAt = DateTime.UtcNow.AddDays(-15),
                UpdatedAt = DateTime.UtcNow.AddDays(-1)
            }
        };

        if (partnerId.HasValue)
        {
            customers = customers.Where(c => c.PartnerId == partnerId.Value).ToList();
        }

        return Ok(customers);
    }

    /// <summary>
    /// Get customer by ID
    /// </summary>
    [HttpGet("{id}")]
    [RequireScope(B2BScopes.CustomerRead)]
    public async Task<ActionResult<Customer>> GetCustomer(int id)
    {
        _logger.LogInformation("Getting customer with ID: {CustomerId}", id);
        
        if (id == 1)
        {
            var customer = new Customer
            {
                Id = 1,
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Phone = "******-1234",
                PassportNumber = "US123456789",
                Nationality = "USA",
                DateOfBirth = new DateTime(1985, 5, 15),
                Status = CustomerStatus.Active,
                PartnerId = 1,
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                UpdatedAt = DateTime.UtcNow.AddDays(-2)
            };
            return Ok(customer);
        }

        return NotFound($"Customer with ID {id} not found");
    }

    /// <summary>
    /// Create a new customer
    /// </summary>
    [HttpPost]
    [RequireScope(B2BScopes.CustomerWrite)]
    public async Task<ActionResult<Customer>> CreateCustomer([FromBody] Customer customer)
    {
        _logger.LogInformation("Creating new customer: {CustomerEmail}", customer.Email);
        
        // In a real implementation, you would save to database
        customer.Id = new Random().Next(1000, 9999);
        customer.CreatedAt = DateTime.UtcNow;
        customer.UpdatedAt = DateTime.UtcNow;
        customer.Status = CustomerStatus.Active;
        
        return CreatedAtAction(nameof(GetCustomer), new { id = customer.Id }, customer);
    }

    /// <summary>
    /// Update an existing customer
    /// </summary>
    [HttpPut("{id}")]
    [RequireScope(B2BScopes.CustomerWrite)]
    public async Task<ActionResult<Customer>> UpdateCustomer(int id, [FromBody] Customer customer)
    {
        _logger.LogInformation("Updating customer with ID: {CustomerId}", id);
        
        if (id != customer.Id)
        {
            return BadRequest("Customer ID mismatch");
        }

        // In a real implementation, you would update in database
        customer.UpdatedAt = DateTime.UtcNow;
        
        return Ok(customer);
    }

    /// <summary>
    /// Delete a customer
    /// </summary>
    [HttpDelete("{id}")]
    [RequireScope(B2BScopes.CustomerWrite)]
    public async Task<ActionResult> DeleteCustomer(int id)
    {
        _logger.LogInformation("Deleting customer with ID: {CustomerId}", id);
        
        // In a real implementation, you would delete from database
        return NoContent();
    }
}
