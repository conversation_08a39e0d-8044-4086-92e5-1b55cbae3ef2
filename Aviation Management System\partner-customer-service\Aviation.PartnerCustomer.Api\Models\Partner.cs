namespace Aviation.PartnerCustomer.Api.Models;

public class Partner
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string CompanyCode { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public PartnerType Type { get; set; }
    public PartnerStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string ApiKey { get; set; } = string.Empty;
    public List<string> AllowedScopes { get; set; } = new();
}

public enum PartnerType
{
    Airline,
    TravelAgency,
    CorporateClient,
    Broker
}

public enum PartnerStatus
{
    Active,
    Inactive,
    Suspended,
    PendingApproval
}

public class Customer
{
    public int Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string PassportNumber { get; set; } = string.Empty;
    public string Nationality { get; set; } = string.Empty;
    public DateTime DateOfBirth { get; set; }
    public CustomerStatus Status { get; set; }
    public int? PartnerId { get; set; }
    public Partner? Partner { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public enum CustomerStatus
{
    Active,
    Inactive,
    Blocked
}
