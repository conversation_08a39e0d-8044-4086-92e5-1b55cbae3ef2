#!/bin/bash

# Aviation Management System - Development Setup Script

set -e

echo "🚀 Setting up Aviation Management System for local development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check prerequisites
print_header "Checking Prerequisites"

# Check Docker
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker Desktop."
    exit 1
fi
print_status "Docker is installed"

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose."
    exit 1
fi
print_status "Docker Compose is installed"

# Check .NET SDK
if ! command -v dotnet &> /dev/null; then
    print_warning ".NET SDK is not installed. Some development features may not work."
else
    print_status ".NET SDK is installed"
fi

# Create necessary directories
print_header "Creating Directory Structure"

directories=(
    "logs/api-gateway"
    "logs/partner-customer"
    "logs/order-management"
    "logs/finance-billing"
    "logs/product-pricing"
    "logs/trip-estimation"
    "logs/document"
    "logs/app-registry"
    "data/sqlserver"
    "scripts/sql"
    "monitoring"
    "monitoring/grafana/dashboards"
    "monitoring/grafana/datasources"
)

for dir in "${directories[@]}"; do
    mkdir -p "$dir"
    print_status "Created directory: $dir"
done

# Create monitoring configuration files
print_header "Setting up Monitoring Configuration"

# Prometheus configuration
cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'aviation-api-gateway'
    static_configs:
      - targets: ['api-gateway:80']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'aviation-microservices'
    static_configs:
      - targets: 
        - 'partner-customer-service:80'
        - 'order-management-service:80'
        - 'finance-billing-service:80'
        - 'product-pricing-service:80'
        - 'trip-estimation-service:80'
        - 'document-service:80'
        - 'app-registry-service:80'
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'sqlserver'
    static_configs:
      - targets: ['sqlserver:1433']
    scrape_interval: 60s
EOF

print_status "Created Prometheus configuration"

# Grafana datasource configuration
cat > monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

print_status "Created Grafana datasource configuration"

# Create SQL initialization scripts
print_header "Creating Database Initialization Scripts"

cat > scripts/sql/01-create-databases.sql << 'EOF'
-- Create databases for all microservices
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AviationPartnerCustomer')
BEGIN
    CREATE DATABASE AviationPartnerCustomer;
END

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AviationOrderManagement')
BEGIN
    CREATE DATABASE AviationOrderManagement;
END

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AviationFinanceBilling')
BEGIN
    CREATE DATABASE AviationFinanceBilling;
END

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AviationProductPricing')
BEGIN
    CREATE DATABASE AviationProductPricing;
END

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AviationTripEstimation')
BEGIN
    CREATE DATABASE AviationTripEstimation;
END

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AviationDocument')
BEGIN
    CREATE DATABASE AviationDocument;
END

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AviationAppRegistry')
BEGIN
    CREATE DATABASE AviationAppRegistry;
END
EOF

print_status "Created database initialization script"

# Create environment file
print_header "Creating Environment Configuration"

cat > .env << 'EOF'
# Aviation Management System - Development Environment

# Database Configuration
SA_PASSWORD=YourStrong!Passw0rd
MSSQL_PID=Express

# OAuth2 Configuration
OAUTH2_AUTHORITY=http://localhost:8080
OAUTH2_AUDIENCE=aviation-api
OAUTH2_ISSUER=http://localhost:8080
OAUTH2_SIGNING_KEY=dev-signing-key-256-bits-minimum-length-required

# Logging Configuration
LOG_LEVEL=Debug
ASPNETCORE_LOG_LEVEL=Information

# Service Ports
API_GATEWAY_PORT=5000
PARTNER_CUSTOMER_PORT=5001
ORDER_MANAGEMENT_PORT=5002
FINANCE_BILLING_PORT=5003
PRODUCT_PRICING_PORT=5004
TRIP_ESTIMATION_PORT=5005
DOCUMENT_SERVICE_PORT=5006
APP_REGISTRY_PORT=5007

# External Services
SQL_SERVER_PORT=1433
REDIS_PORT=6379
SEQ_PORT=5341
JAEGER_PORT=16686
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
EOF

print_status "Created environment configuration file"

# Build and start services
print_header "Building and Starting Services"

print_status "Building Docker images..."
docker-compose build --parallel

print_status "Starting infrastructure services..."
docker-compose up -d sqlserver redis seq jaeger prometheus grafana

print_status "Waiting for SQL Server to be ready..."
sleep 30

print_status "Starting microservices..."
docker-compose up -d

print_status "Waiting for all services to be healthy..."
sleep 60

# Verify services
print_header "Verifying Service Health"

services=(
    "http://localhost:5000/health:API Gateway"
    "http://localhost:5001/health:Partner Customer Service"
    "http://localhost:5002/health:Order Management Service"
    "http://localhost:5003/health:Finance Billing Service"
    "http://localhost:5004/health:Product Pricing Service"
    "http://localhost:5005/health:Trip Estimation Service"
    "http://localhost:5006/health:Document Service"
    "http://localhost:5007/health:App Registry Service"
)

for service in "${services[@]}"; do
    url="${service%%:*}"
    name="${service##*:}"
    
    if curl -f -s "$url" > /dev/null; then
        print_status "$name is healthy ✓"
    else
        print_warning "$name is not responding ⚠️"
    fi
done

# Display access information
print_header "Development Environment Ready!"

echo ""
echo "🌐 Service URLs:"
echo "   API Gateway:           http://localhost:5000"
echo "   Partner Customer API:  http://localhost:5001"
echo "   Order Management API:  http://localhost:5002"
echo "   Finance Billing API:   http://localhost:5003"
echo "   Product Pricing API:   http://localhost:5004"
echo "   Trip Estimation API:   http://localhost:5005"
echo "   Document API:          http://localhost:5006"
echo "   App Registry API:      http://localhost:5007"
echo ""
echo "🔧 Development Tools:"
echo "   Seq (Logging):         http://localhost:5341"
echo "   Jaeger (Tracing):      http://localhost:16686"
echo "   Prometheus (Metrics):  http://localhost:9090"
echo "   Grafana (Dashboards):  http://localhost:3000 (admin/admin)"
echo ""
echo "💾 Database:"
echo "   SQL Server:            localhost:1433 (sa/YourStrong!Passw0rd)"
echo "   Redis:                 localhost:6379"
echo ""
echo "📚 API Documentation:"
echo "   Swagger UI:            http://localhost:5000/swagger"
echo ""
echo "🎉 Setup complete! Happy coding!"
echo ""
echo "💡 Useful commands:"
echo "   View logs:             docker-compose logs -f [service-name]"
echo "   Stop all services:     docker-compose down"
echo "   Rebuild service:       docker-compose build [service-name]"
echo "   Reset everything:      docker-compose down -v && docker-compose up -d"
EOF

chmod +x scripts/dev-setup.sh
print_status "Created development setup script"

<function_calls>
<invoke name="update_tasks">
<parameter name="tasks">[{"task_id": "1sNvUmoSFomt7dXvMFriCy", "state": "COMPLETE"}]
